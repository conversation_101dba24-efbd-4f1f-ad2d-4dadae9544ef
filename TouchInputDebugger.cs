using UnityEngine;
using UnityEngine.InputSystem;

public class TouchInputDebugger : MonoBehaviour
{
    [Header("Debug Settings")]
    public bool enableDebugLogs = true;
    public bool showOnScreenDebug = true;
    
    private string debugText = "";
    
    void Update()
    {
        debugText = "";
        
        // Check New Input System touch
        if (Touchscreen.current != null)
        {
            debugText += "New Input System Touch Available\n";
            
            if (Touchscreen.current.primaryTouch.press.isPressed)
            {
                Vector2 touchPos = Touchscreen.current.primaryTouch.position.ReadValue();
                Vector2 touchDelta = Touchscreen.current.primaryTouch.delta.ReadValue();
                debugText += $"Primary Touch: Pos={touchPos}, Delta={touchDelta}\n";
                
                if (enableDebugLogs)
                {
                    Debug.Log($"New Input System Touch - Pos: {touchPos}, Delta: {touchDelta}");
                }
            }
            
            int touchCount = 0;
            for (int i = 0; i < Touchscreen.current.touches.Count; i++)
            {
                if (Touchscreen.current.touches[i].press.isPressed)
                {
                    touchCount++;
                }
            }
            debugText += $"Active Touches: {touchCount}\n";
        }
        else
        {
            debugText += "New Input System Touch NOT Available\n";
        }
        
        // Check Legacy Input System touch
        if (Input.touchSupported)
        {
            debugText += $"Legacy Touch Supported: {Input.touchCount} touches\n";
            
            for (int i = 0; i < Input.touchCount; i++)
            {
                Touch touch = Input.GetTouch(i);
                debugText += $"Touch {i}: Pos={touch.position}, Phase={touch.phase}, Delta={touch.deltaPosition}\n";

                if (enableDebugLogs && touch.phase == UnityEngine.TouchPhase.Moved)
                {
                    Debug.Log($"Legacy Touch {i} - Pos: {touch.position}, Delta: {touch.deltaPosition}");
                }
            }
        }
        else
        {
            debugText += "Legacy Touch NOT Supported\n";
        }
        
        // Check screen dimensions
        debugText += $"Screen: {Screen.width}x{Screen.height}\n";
        
        // Check if we're in mobile simulator
        debugText += $"Platform: {Application.platform}\n";
        debugText += $"Device Type: {SystemInfo.deviceType}\n";
    }
    
    void OnGUI()
    {
        if (showOnScreenDebug)
        {
            GUI.color = Color.white;
            GUI.backgroundColor = Color.black;
            GUIStyle style = new GUIStyle(GUI.skin.box);
            style.alignment = TextAnchor.UpperLeft;
            style.fontSize = 12;
            
            GUI.Box(new Rect(10, 10, 400, 200), debugText, style);
        }
    }
}
