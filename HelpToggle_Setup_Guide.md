# Help Toggle (F1 Key) Setup Guide

## Overview
This guide will help you set up the help menu toggle functionality using the F1 key. The system supports both the New Input System and Legacy Input System.

## Quick Setup Steps

### 1. **Add the InputSystemSetupHelper (Recommended)**
1. Create an empty GameObject in your scene
2. Name it "InputSystemSetupHelper"
3. Add the `InputSystemSetupHelper` component to it
4. In the Inspector, check "Auto Configure On Start"
5. Set "Use New Input System" to `true` (recommended)
6. Click "Configure Input System" in the Inspector (or it will auto-configure on play)

### 2. **Manual Setup (Alternative)**

#### For New Input System (Recommended):
1. **Enable New Input System on Vehicle Controller:**
   - Find your vehicle's `JrsVehicleController` component
   - Set `useNewInputSystem = true`

2. **Ensure JrsNewInputController is Active:**
   - Make sure you have a `JrsNewInputController` component in your scene
   - Ensure it's enabled and properly configured

3. **Configure Help Toggle Object:**
   - Find your `JrsToggleObject` component (attached to help menu)
   - Set `useNewInputSystem = true`
   - Assign your help menu GameObject to `objectToToggle`

#### For Legacy Input System:
1. **Disable New Input System on Vehicle Controller:**
   - Find your vehicle's `JrsVehicleController` component
   - Set `useNewInputSystem = false`

2. **Configure Help Toggle Object:**
   - Find your `JrsToggleObject` component
   - Set `useNewInputSystem = false`
   - Set `toggleKey = KeyCode.F1`
   - Assign your help menu GameObject to `objectToToggle`

## Key Bindings

### New Input System:
- **Keyboard:** F1 key
- **Gamepad:** Start button
- **Mobile:** Touch button (if configured)

### Legacy Input System:
- **Keyboard:** F1 key (configurable via toggleKey field)

## Troubleshooting

### F1 Key Not Working?

1. **Check Input System Configuration:**
   ```
   - Use the InputSystemSetupHelper to auto-configure
   - Or manually verify that all components use the same input system
   ```

2. **Verify Component Setup:**
   ```
   - JrsToggleObject component is attached to a GameObject
   - objectToToggle field is assigned to your help menu
   - useNewInputSystem matches your vehicle controller setting
   ```

3. **Check for Conflicts:**
   ```
   - Only one input controller should be active at a time
   - Don't have both JrsInputController and JrsNewInputController enabled
   ```

4. **Test Input Detection:**
   ```
   - Add HelpToggleDebugger component to any GameObject
   - Press F1 and check console for debug messages
   ```

### Common Issues:

#### Issue: "F1 key detected but help menu doesn't toggle"
**Solution:** Check that `objectToToggle` is assigned in JrsToggleObject component

#### Issue: "Multiple input controllers active"
**Solution:** Use InputSystemSetupHelper to properly configure and disable conflicting controllers

#### Issue: "New Input System not working"
**Solution:** 
- Ensure Unity's Input System package is installed
- Verify VehicleInputActions.inputactions file exists
- Check that JrsNewInputController is enabled

#### Issue: "Help toggle works but other vehicle controls don't"
**Solution:** Make sure the vehicle controller's `useNewInputSystem` setting matches your setup

## Testing

### Method 1: Use InputSystemSetupHelper
1. Add InputSystemSetupHelper component to any GameObject
2. Click "Test F1 Key Detection" in the Inspector
3. Press F1 and check console for detection messages

### Method 2: Manual Testing
1. Add HelpToggleDebugger component to any GameObject
2. Enable debug logs in the component
3. Press F1 and observe console output

### Method 3: Visual Testing
1. Ensure your help menu GameObject is assigned to JrsToggleObject
2. Press F1 in play mode
3. Help menu should appear/disappear

## Mobile Touch Support

To add mobile touch support for help toggle:

1. **Create UI Button:**
   - Add a UI Button to your mobile control canvas
   - Position it appropriately (e.g., top-right corner)
   - Set button text to "HELP" or use a help icon

2. **Add JrsCustomButton Component:**
   - Add `JrsCustomButton` component to the button
   - This enables touch detection

3. **Connect to Input Controller:**
   - Find your `JrsNewInputController` component
   - Assign the button to the `helpToggleButton` field
   - Ensure `enableMobileControls = true`

## Advanced Configuration

### Custom Key Binding (Legacy System):
```csharp
// In JrsToggleObject component
public KeyCode toggleKey = KeyCode.F1; // Change to any key
```

### Custom Input Action (New Input System):
1. Open VehicleInputActions.inputactions in Unity
2. Find the "HelpToggle" action
3. Modify the bindings as needed
4. Regenerate the C# class

## Support

If you're still having issues:

1. **Check Console:** Look for error messages or warnings
2. **Use Debug Tools:** InputSystemSetupHelper and HelpToggleDebugger
3. **Verify Setup:** Ensure all components are properly configured
4. **Test Incrementally:** Start with InputSystemSetupHelper auto-configuration

The help toggle system is designed to work seamlessly with both input systems and automatically detect the appropriate configuration.
