using UnityEngine;
using UnityEngine.UI;
using System.Linq;

/// <summary>
/// Test script for the Vehicle Proximity Selection System
/// Provides debugging information and testing capabilities
/// </summary>
public class VehicleProximityTester : MonoBehaviour
{
    [Header("System References")]
    public JrsVehicleSelector vehicleSelector;
    public JrsVehicleProximitySelector proximitySelector;
    public JrsVehicleProximityMessage proximityMessage;
    public JrsNewInputController inputController;
    
    [Header("Test Settings")]
    public bool enableDebugUI = true;
    public bool enableConsoleLogging = true;
    public KeyCode testKey = KeyCode.T;
    
    [Header("Debug Info")]
    [SerializeField] private bool systemInitialized = false;
    [SerializeField] private int currentVehicleIndex = -1;
    [SerializeField] private string currentVehicleName = "None";
    [SerializeField] private int nearbyVehicleCount = 0;
    [SerializeField] private string nearestVehicleName = "None";
    [SerializeField] private float distanceToNearest = 0f;
    [SerializeField] private bool messageVisible = false;
    
    // UI Elements for runtime debugging
    private Canvas debugCanvas;
    private Text debugText;
    
    void Start()
    {
        InitializeSystem();
        if (enableDebugUI)
        {
            CreateDebugUI();
        }
    }
    
    void Update()
    {
        if (systemInitialized)
        {
            UpdateDebugInfo();
            
            if (enableDebugUI && debugText != null)
            {
                UpdateDebugUI();
            }
        }
        
        // Test key functionality
        if (Input.GetKeyDown(testKey))
        {
            TestProximitySelection();
        }
    }
    
    private void InitializeSystem()
    {
        // Find references if not assigned
        if (vehicleSelector == null)
            vehicleSelector = FindObjectOfType<JrsVehicleSelector>();
            
        if (proximitySelector == null)
            proximitySelector = FindObjectOfType<JrsVehicleProximitySelector>();
            
        if (proximityMessage == null)
            proximityMessage = FindObjectOfType<JrsVehicleProximityMessage>();
            
        if (inputController == null)
            inputController = FindObjectOfType<JrsNewInputController>();
        
        // Check if system is properly initialized
        systemInitialized = vehicleSelector != null && proximitySelector != null && 
                           proximityMessage != null;
        
        if (enableConsoleLogging)
        {
            if (systemInitialized)
            {
                Debug.Log("[ProximityTester] System initialized successfully!");
            }
            else
            {
                Debug.LogWarning("[ProximityTester] System initialization failed - missing components!");
                LogMissingComponents();
            }
        }
    }
    
    private void LogMissingComponents()
    {
        if (vehicleSelector == null) Debug.LogError("[ProximityTester] Missing: JrsVehicleSelector");
        if (proximitySelector == null) Debug.LogError("[ProximityTester] Missing: JrsVehicleProximitySelector");
        if (proximityMessage == null) Debug.LogError("[ProximityTester] Missing: JrsVehicleProximityMessage");
        if (inputController == null) Debug.LogWarning("[ProximityTester] Missing: JrsNewInputController (gamepad input may not work)");
    }
    
    private void UpdateDebugInfo()
    {
        if (vehicleSelector != null)
        {
            currentVehicleIndex = vehicleSelector.GetCurrentVehicleIndex();
            currentVehicleName = currentVehicleIndex >= 0 &&
                                currentVehicleIndex < vehicleSelector.vehicles.Count ?
                                vehicleSelector.vehicles[currentVehicleIndex].name : "None";
        }
        
        if (proximitySelector != null)
        {
            nearbyVehicleCount = proximitySelector.GetNearbyVehicleCount();
            var nearestVehicle = proximitySelector.GetNearestSelectableVehicle();
            nearestVehicleName = nearestVehicle != null ? nearestVehicle.name : "None";
            
            if (nearestVehicle != null && vehicleSelector != null && vehicleSelector.vehicles.Count > currentVehicleIndex && currentVehicleIndex >= 0)
            {
                var currentVehicle = vehicleSelector.vehicles[currentVehicleIndex];
                distanceToNearest = Vector3.Distance(currentVehicle.transform.position, nearestVehicle.transform.position);
            }
            else
            {
                distanceToNearest = 0f;
            }
        }
        
        // Check if message is visible (approximate)
        messageVisible = nearbyVehicleCount > 0 && nearestVehicleName != "None";
    }
    
    private void CreateDebugUI()
    {
        // Create canvas for debug UI
        GameObject canvasObj = new GameObject("ProximityTesterDebugCanvas");
        canvasObj.transform.SetParent(transform);
        
        debugCanvas = canvasObj.AddComponent<Canvas>();
        debugCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
        debugCanvas.sortingOrder = 1000; // Very high to appear on top
        
        var canvasScaler = canvasObj.AddComponent<CanvasScaler>();
        canvasScaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        canvasScaler.referenceResolution = new Vector2(1920, 1080);
        
        canvasObj.AddComponent<GraphicRaycaster>();
        
        // Create debug text
        GameObject textObj = new GameObject("DebugText");
        textObj.transform.SetParent(debugCanvas.transform);
        
        debugText = textObj.AddComponent<Text>();
        debugText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        if (debugText.font == null)
        {
            debugText.font = Resources.FindObjectsOfTypeAll<Font>().FirstOrDefault();
        }
        debugText.fontSize = 16;
        debugText.color = Color.white;
        debugText.alignment = TextAnchor.UpperLeft;
        
        // Add outline for better visibility
        var outline = textObj.AddComponent<Outline>();
        outline.effectColor = Color.black;
        outline.effectDistance = new Vector2(1, 1);
        
        // Position the text (top-left corner)
        RectTransform rectTransform = debugText.GetComponent<RectTransform>();
        rectTransform.anchorMin = new Vector2(0, 1);
        rectTransform.anchorMax = new Vector2(0, 1);
        rectTransform.pivot = new Vector2(0, 1);
        rectTransform.anchoredPosition = new Vector2(10, -10);
        rectTransform.sizeDelta = new Vector2(400, 300);
    }
    
    private void UpdateDebugUI()
    {
        if (debugText == null) return;
        
        string debugInfo = "=== Vehicle Proximity System Debug ===\n";
        debugInfo += $"System Initialized: {systemInitialized}\n";
        debugInfo += $"Current Vehicle: {currentVehicleName} (Index: {currentVehicleIndex})\n";
        debugInfo += $"Nearby Vehicles: {nearbyVehicleCount}\n";
        debugInfo += $"Nearest Vehicle: {nearestVehicleName}\n";
        debugInfo += $"Distance to Nearest: {distanceToNearest:F2}m\n";
        debugInfo += $"Message Visible: {messageVisible}\n";
        debugInfo += $"Detection Range: {(proximitySelector != null ? proximitySelector.DetectionRange.ToString("F1") + "m" : "N/A")}\n";
        debugInfo += $"\nPress '{testKey}' to test proximity selection";
        
        debugText.text = debugInfo;
    }
    
    [ContextMenu("Test Proximity Selection")]
    public void TestProximitySelection()
    {
        if (!systemInitialized)
        {
            Debug.LogWarning("[ProximityTester] Cannot test - system not initialized!");
            return;
        }
        
        if (enableConsoleLogging)
        {
            Debug.Log("[ProximityTester] Testing proximity selection...");
            Debug.Log($"[ProximityTester] Nearby vehicles: {nearbyVehicleCount}");
            Debug.Log($"[ProximityTester] Can select nearest: {proximitySelector.CanSelectNearestVehicle()}");
        }
        
        if (proximitySelector.CanSelectNearestVehicle())
        {
            bool success = proximitySelector.SelectNearestVehicle();
            if (enableConsoleLogging)
            {
                Debug.Log($"[ProximityTester] Selection result: {success}");
            }
        }
        else
        {
            if (enableConsoleLogging)
            {
                Debug.Log("[ProximityTester] No vehicle available for selection");
            }
        }
    }
    
    [ContextMenu("Force System Reinitialization")]
    public void ForceReinitialize()
    {
        InitializeSystem();
        if (enableConsoleLogging)
        {
            Debug.Log("[ProximityTester] System reinitialized");
        }
    }
    
    void OnDestroy()
    {
        if (debugCanvas != null)
        {
            Destroy(debugCanvas.gameObject);
        }
    }
}
