# Vehicle Proximity Selection System - COMPLETE SETUP GUIDE
## 🚗 What This Does
When you drive close to another car, a button appears above it. Press the gamepad button to jump into that car!

---

# 📋 STEP-BY-STEP SETUP (Follow Every Step!)

## STEP 1: Create the Image Folder 📁

1. **Open Unity Project window** (bottom of screen)
2. **Navigate to:** `Assets` → `Police Pursuit Vehicle` → `Script`
3. **Right-click in the Script folder** → `Create` → `Folder`
4. **Name the new folder:** `Resources` (exactly this name!)
5. **Double-click the Resources folder** to open it
6. **Right-click inside Resources folder** → `Create` → `Folder`
7. **Name this folder:** `ui` (exactly this name!)
8. **Copy your select_to_enter.png file** into the `ui` folder
9. **Final path should be:** `Assets/Police Pursuit Vehicle/Script/Resources/ui/select_to_enter.png`

## STEP 2: Create the Proximity Selector 🎯

1. **In Hierarchy window** (left side), right-click in empty space
2. **Click:** `Create Empty`
3. **Name it:** `VehicleProximitySelector` (exactly this name!)
4. **Click on this new object** to select it
5. **In Inspector window** (right side), click `Add Component`
6. **Type:** `JrsVehicleProximitySelector`
7. **Click on it** to add the component
8. **Set these values in Inspector:**
   - Detection Range: `10`
   - Update Interval: `0.1`
   - Vehicle Layer Mask: `Everything`

## STEP 3: Create the Billboard UI 🎪

### A. Create the Main Billboard Object
1. **In Hierarchy window**, right-click in empty space
2. **Click:** `Create Empty`
3. **Name it:** `VehicleSelectionBillboard` (exactly this name!)
4. **Click on this new object** to select it
5. **In Inspector window**, click `Add Component`
6. **Type:** `JrsVehicleSelectionBillboard`
7. **Click on it** to add the component
8. **Set these values in Inspector:**
   - Height Offset: `3`
   - Scale Multiplier: `1`
   - Min Scale: `0.5`
   - Max Scale: `2`
   - Max Distance: `20`

### B. Create the Canvas (UI Container)
1. **Right-click on VehicleSelectionBillboard** in Hierarchy
2. **Click:** `UI` → `Canvas`
3. **A Canvas will be created as a child** - rename it to `BillboardCanvas`
4. **Click on BillboardCanvas** to select it
5. **In Inspector, find Canvas component:**
   - Set Render Mode: `World Space`
   - Set Sorting Layer: `Default`
   - Set Order in Layer: `10`
6. **In Rect Transform component:**
   - Set Width: `200`
   - Set Height: `100`
   - Set Scale X: `0.01`, Y: `0.01`, Z: `0.01`

### C. Create the Background Image
1. **Right-click on BillboardCanvas** in Hierarchy
2. **Click:** `UI` → `Image`
3. **Rename it to:** `SelectImage`
4. **Click on SelectImage** to select it
5. **In Inspector, find Image component:**
   - Click the small circle next to `Source Image`
   - Type `select_to_enter` in the search box
   - Select your `select_to_enter` image
6. **In Rect Transform:**
   - Click the anchor preset button (square icon)
   - Hold Shift+Alt and click the center-middle preset
   - Set Width: `180`
   - Set Height: `60`

### D. Create the Button
1. **Right-click on BillboardCanvas** in Hierarchy
2. **Click:** `UI` → `Button - TextMeshPro`
3. **Rename it to:** `SelectButton`
4. **Click on SelectButton** to select it
5. **In Inspector, find Button component:**
   - Leave all settings as default for now
6. **In Rect Transform:**
   - Click the anchor preset button (square icon)
   - Hold Shift+Alt and click the center-middle preset
   - Set Width: `180`
   - Set Height: `60`
7. **Find the Text child object** under SelectButton
8. **Click on the Text child** and in Inspector:
   - Change text to: `SELECT TO ENTER`
   - Set Font Size: `14`
   - Set Color: `White`

## STEP 4: Connect Everything Together 🔗

### A. Fix the Vehicle Selector
1. **Find your JrsVehicleSelector object** in Hierarchy
2. **Click on it** to select it
3. **In Inspector, find "Proximity Selection" section**
4. **Check the box** next to "Enable Proximity Selection" ✅
5. **For "Proximity Selector" field:**
   - Click the small circle ⭕ next to it
   - Select `VehicleProximitySelector` from the list
6. **For "Selection Billboard" field:**
   - Click the small circle ⭕ next to it
   - Select `VehicleSelectionBillboard` from the list

### B. Fix the Proximity Selector References
1. **Click on VehicleProximitySelector** in Hierarchy
2. **In Inspector, find "References" section**
3. **For "Vehicle Selector" field:**
   - Click the small circle ⭕ next to it
   - Select your `JrsVehicleSelector` object from the list

### C. Fix the Billboard References
1. **Click on VehicleSelectionBillboard** in Hierarchy
2. **In Inspector, find "UI References" section**
3. **For "Canvas" field:**
   - Click the small circle ⭕ next to it
   - Select `BillboardCanvas` from the list
4. **For "Select Image" field:**
   - Click the small circle ⭕ next to it
   - Select `SelectImage` from the list
5. **For "Select Button" field:**
   - Click the small circle ⭕ next to it
   - Select `SelectButton` from the list

### D. Fix the Input Controller
1. **Find your JrsNewInputController object** in Hierarchy
2. **Click on it** to select it
3. **In Inspector, find "Vehicle Selection" section**
4. **For "Proximity Selector" field:**
   - Click the small circle ⭕ next to it
   - Select `VehicleProximitySelector` from the list

---

# 🧪 TESTING YOUR SETUP

## STEP 5: Test Everything Works

### Before You Test - Check This List! ✅
1. **Image file is in correct location:** `Assets/Police Pursuit Vehicle/Script/Resources/ui/select_to_enter.png`
2. **All reference fields are filled** (no "None" showing)
3. **Enable Proximity Selection is checked** ✅
4. **You have at least 2 vehicles** in your scene

### How to Test:
1. **Press Play** ▶️
2. **Check Console window** (Window → General → Console)
3. **Look for these messages:**
   - "JrsVehicleProximitySelector initialized successfully"
   - "JrsVehicleSelectionBillboard initialized successfully"
4. **Drive your car VERY CLOSE** (less than 10 units) to another vehicle
5. **You should see a button appear** above the other vehicle
6. **Press the gamepad button** (A on Xbox, X on PlayStation) to switch vehicles

---

# 🚨 TROUBLESHOOTING

## Problem: "None (Missing)" in Inspector
**Solution:** You didn't connect the references! Go back to STEP 4 and drag the objects to the empty fields.

## Problem: No button appears above vehicles
**Check these:**
1. **Image file location:** Must be in `Script/Resources/ui/select_to_enter.png`
2. **Distance:** Drive VERY close (less than 10 units)
3. **Console messages:** Look for error messages
4. **Enable Proximity Selection:** Must be checked ✅

## Problem: Button appears but doesn't work
**Check these:**
1. **Input Controller reference:** Must be connected to VehicleProximitySelector
2. **Press 'T' key instead of gamepad** (T key should work for testing)
3. **Console errors:** Look for error messages

## Problem: Nothing happens when pressing 'T'
**Check these:**
1. **All references connected:** No "None" fields should be showing
2. **Vehicle distance:** Must be less than 10 units apart
3. **Console messages:** Should show initialization messages
4. **Input system:** Make sure you have the new Input System package installed

## Problem: Console shows errors about missing image
**Solution:**
1. **Check image path:** `Assets/Police Pursuit Vehicle/Script/Resources/ui/select_to_enter.png`
2. **Check image name:** Must be exactly `select_to_enter.png`
3. **Reimport image:** Right-click image → Reimport

---

# 📝 FINAL CHECKLIST

Before asking for help, make sure you completed ALL these steps:

- [ ] ✅ Created Resources/ui folder in correct location
- [ ] ✅ Copied select_to_enter.png to Resources/ui folder
- [ ] ✅ Created VehicleProximitySelector GameObject
- [ ] ✅ Added JrsVehicleProximitySelector component
- [ ] ✅ Created VehicleSelectionBillboard GameObject
- [ ] ✅ Added JrsVehicleSelectionBillboard component
- [ ] ✅ Connected Vehicle Selector reference in JrsVehicleSelector
- [ ] ✅ Connected Selection Billboard reference in JrsVehicleSelector
- [ ] ✅ Connected Proximity Selector reference in JrsVehicleProximitySelector
- [ ] ✅ Connected Proximity Selector reference in JrsNewInputController
- [ ] ✅ Checked "Enable Proximity Selection" box
- [ ] ✅ Tested with vehicles less than 10 units apart
- [ ] ✅ Checked Console for initialization messages

**If you completed ALL steps and it still doesn't work, send me a screenshot of your Console window with any error messages!**

### 4. UI Asset Setup - CRITICAL STEP
The system looks for the `select_to_enter.png` image:

**EXACT FOLDER STRUCTURE REQUIRED:**
```
Assets/
└── Police Pursuit Vehicle/
    └── Script/
        └── Resources/          ← Create this folder
            └── ui/             ← Create this folder
                └── select_to_enter.png  ← Place image here
```

**Steps:**
1. Navigate to: `Assets/Police Pursuit Vehicle/Script/`
2. Right-click → Create → Folder → Name: `Resources`
3. Inside Resources → Right-click → Create → Folder → Name: `ui`
4. Copy your `select_to_enter.png` into the `ui` folder
5. In Unity, the path should show as: `Resources/ui/select_to_enter`

## Usage

### For Players
1. **Drive near another vehicle** (within detection range)
2. **Billboard appears** above the nearest selectable vehicle
3. **Select the vehicle** using:
   - **Gamepad**: Press SELECT button (South button)
   - **Mouse**: Click on the billboard or the vehicle
   - **Touch**: Tap on the billboard or the vehicle
4. **Switch instantly** to the selected vehicle

### Backward Compatibility
- **Page Up/Page Down**: Still cycles through vehicles
- **Mouse clicking vehicles**: Still works as before
- **All existing functionality**: Preserved

## Configuration

### Detection Range
```csharp
proximitySelector.SetDetectionRange(15f); // 15 meter range
```

### Billboard Appearance
```csharp
billboard.SetHeightOffset(4f);        // 4 meters above vehicle
billboard.SetScaleMultiplier(1.5f);   // 1.5x scale
billboard.SetTransitionSpeed(8f);     // Faster transitions
```

### Enable/Disable System
```csharp
vehicleSelector.SetProximitySelectionEnabled(false); // Disable
vehicleSelector.SetProximitySelectionEnabled(true);  // Enable
```

## Troubleshooting

### Billboard Not Appearing
1. Check that `enableProximitySelection = true` in JrsVehicleSelector
2. Verify vehicles are within detection range
3. Ensure vehicles have colliders for detection
4. Check that vehicles are in the JrsVehicleSelector's vehicle list

### Gamepad Not Working
1. Verify New Input System is enabled in Project Settings
2. Check that VehicleInputActions asset is assigned in JrsNewInputController
3. Ensure gamepad is connected and recognized by Unity
4. Check that proximitySelector reference is assigned in JrsNewInputController

### Performance Issues
1. Increase `updateInterval` in JrsVehicleProximitySelector (e.g., 0.2s)
2. Reduce `detectionRange` if you have many vehicles
3. Use appropriate `vehicleLayerMask` to limit detection scope

### UI Issues
1. Ensure EventSystem exists in scene for UI interaction
2. Check Canvas settings on the billboard
3. Verify Camera.main is properly set

## Debug Features

### Visual Debug
Select the `JrsVehicleProximitySelector` in the scene to see:
- Yellow wireframe sphere showing detection range
- Green line connecting to nearest selectable vehicle
- Green wireframe cube around nearest vehicle

### Console Logs
The system provides detailed logging for:
- Vehicle detection and selection
- Input events
- System initialization
- Error conditions

## API Reference

### JrsVehicleProximitySelector
- `NearestSelectableVehicle`: Get the nearest vehicle
- `DetectionRange`: Current detection range
- `HasNearbyVehicles`: Check if any vehicles are nearby
- `CanSelectNearestVehicle()`: Check if selection is possible
- `SelectNearestVehicle()`: Perform vehicle selection
- `GetNearbyVehicles()`: Get list of all nearby vehicles
- `GetDistanceToNearestVehicle()`: Get distance to nearest vehicle

### JrsVehicleSelectionBillboard
- `IsVisible`: Check if billboard is currently shown
- `TargetVehicle`: Get the vehicle the billboard is following
- `ShowBillboard(Transform)`: Manually show billboard
- `HideBillboard()`: Manually hide billboard

### Events
- `JrsVehicleProximitySelector.OnNearestVehicleChanged`: Fired when nearest vehicle changes
- `JrsVehicleSelectionBillboard.OnBillboardClicked`: Fired when billboard is clicked

## Performance Notes
- Detection uses `Physics.OverlapSphere` which is efficient for reasonable ranges
- Update interval can be adjusted based on your needs
- System automatically handles vehicle list changes
- Billboard uses smooth transitions that can be disabled for better performance

## Compatibility
- Unity 2020.3 LTS or newer
- New Input System package
- Works with existing JRS Vehicle Controller system
- Compatible with mobile, desktop, and console platforms
