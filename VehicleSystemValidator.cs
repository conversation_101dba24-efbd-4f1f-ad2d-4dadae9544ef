using UnityEngine;

/// <summary>
/// Validation script to check for common issues in the Vehicle Proximity System
/// </summary>
public class VehicleSystemValidator : MonoBehaviour
{
    [Header("Validation Settings")]
    [SerializeField] private bool validateOnStart = true;
    [SerializeField] private bool enableDetailedLogging = true;
    
    [Header("System Status")]
    [SerializeField] private bool allSystemsValid = false;
    [SerializeField] private int errorsFound = 0;
    [SerializeField] private int warningsFound = 0;

    // Public getter for external access
    public bool AllSystemsValid => allSystemsValid;
    
    void Start()
    {
        if (validateOnStart)
        {
            ValidateSystem();
        }
    }
    
    [ContextMenu("Validate Vehicle Proximity System")]
    public void ValidateSystem()
    {
        Log("=== Vehicle Proximity System Validation ===");
        errorsFound = 0;
        warningsFound = 0;
        
        // Check core components
        ValidateVehicleSelector();
        ValidateProximitySelector();
        ValidateProximityMessage();
        ValidateInputController();
        
        // Check for common issues
        CheckForDuplicateComponents();
        CheckForMissingReferences();
        CheckForObsoleteComponents();
        
        // Final report
        GenerateValidationReport();
    }
    
    private void ValidateVehicleSelector()
    {
        Log("--- Validating JrsVehicleSelector ---");
        
        var vehicleSelector = FindObjectOfType<JrsVehicleSelector>();
        if (vehicleSelector == null)
        {
            LogError("JrsVehicleSelector not found in scene!");
            return;
        }
        
        // Check if proximity selection is enabled
        if (!vehicleSelector.enableProximitySelection)
        {
            LogWarning("Proximity selection is disabled in JrsVehicleSelector");
        }
        
        // Check if vehicles list is populated
        if (vehicleSelector.vehicles == null || vehicleSelector.vehicles.Count == 0)
        {
            LogError("No vehicles found in JrsVehicleSelector.vehicles list!");
        }
        else
        {
            Log($"✓ Found {vehicleSelector.vehicles.Count} vehicles in selector");
        }
        
        // Check proximity message reference
        var proximityMessage = vehicleSelector.GetProximityMessage();
        if (proximityMessage == null)
        {
            LogWarning("Proximity message reference is null in JrsVehicleSelector");
        }
        else
        {
            Log("✓ Proximity message reference is set");
        }
    }
    
    private void ValidateProximitySelector()
    {
        Log("--- Validating JrsVehicleProximitySelector ---");
        
        var proximitySelector = FindObjectOfType<JrsVehicleProximitySelector>();
        if (proximitySelector == null)
        {
            LogError("JrsVehicleProximitySelector not found in scene!");
            return;
        }
        
        // Check detection range
        if (proximitySelector.DetectionRange <= 0)
        {
            LogError("Detection range must be greater than 0!");
        }
        else
        {
            Log($"✓ Detection range: {proximitySelector.DetectionRange}m");
        }
        
        Log("✓ JrsVehicleProximitySelector validation passed");
    }
    
    private void ValidateProximityMessage()
    {
        Log("--- Validating JrsVehicleProximityMessage ---");
        
        var proximityMessage = FindObjectOfType<JrsVehicleProximityMessage>();
        if (proximityMessage == null)
        {
            LogError("JrsVehicleProximityMessage not found in scene!");
            return;
        }
        
        Log("✓ JrsVehicleProximityMessage found");
    }
    
    private void ValidateInputController()
    {
        Log("--- Validating JrsNewInputController ---");
        
        var inputController = FindObjectOfType<JrsNewInputController>();
        if (inputController == null)
        {
            LogWarning("JrsNewInputController not found - gamepad input may not work");
            return;
        }
        
        Log("✓ JrsNewInputController found");
    }
    
    private void CheckForDuplicateComponents()
    {
        Log("--- Checking for Duplicate Components ---");
        
        // Check for multiple vehicle selectors
        var vehicleSelectors = FindObjectsOfType<JrsVehicleSelector>();
        if (vehicleSelectors.Length > 1)
        {
            LogWarning($"Found {vehicleSelectors.Length} JrsVehicleSelector components - only one should exist");
        }
        
        // Check for multiple proximity selectors
        var proximitySelectors = FindObjectsOfType<JrsVehicleProximitySelector>();
        if (proximitySelectors.Length > 1)
        {
            LogWarning($"Found {proximitySelectors.Length} JrsVehicleProximitySelector components - only one should exist");
        }
        
        // Check for multiple proximity messages
        var proximityMessages = FindObjectsOfType<JrsVehicleProximityMessage>();
        if (proximityMessages.Length > 1)
        {
            LogWarning($"Found {proximityMessages.Length} JrsVehicleProximityMessage components - only one should exist");
        }
        
        if (vehicleSelectors.Length == 1 && proximitySelectors.Length == 1 && proximityMessages.Length == 1)
        {
            Log("✓ No duplicate components found");
        }
    }
    
    private void CheckForMissingReferences()
    {
        Log("--- Checking for Missing References ---");
        
        var vehicleSelector = FindObjectOfType<JrsVehicleSelector>();
        var proximitySelector = FindObjectOfType<JrsVehicleProximitySelector>();
        
        if (vehicleSelector != null && proximitySelector != null)
        {
            if (vehicleSelector.proximitySelector != proximitySelector)
            {
                LogWarning("JrsVehicleSelector.proximitySelector reference is not set correctly");
            }
            else
            {
                Log("✓ Vehicle selector -> proximity selector reference is correct");
            }
        }
    }
    
    private void CheckForObsoleteComponents()
    {
        Log("--- Checking for Obsolete Components ---");
        
        // Check for old billboard components
        var billboards = FindObjectsOfType<JrsVehicleSelectionBillboard>();
        if (billboards.Length > 0)
        {
            LogWarning($"Found {billboards.Length} obsolete JrsVehicleSelectionBillboard components - consider removing them");
        }
        else
        {
            Log("✓ No obsolete billboard components found");
        }
    }
    
    private void GenerateValidationReport()
    {
        Log("=== Validation Report ===");
        
        if (errorsFound == 0 && warningsFound == 0)
        {
            Log("✅ All systems valid! Vehicle Proximity System is ready to use.");
            allSystemsValid = true;
        }
        else
        {
            Log($"❌ Validation completed with {errorsFound} errors and {warningsFound} warnings");
            allSystemsValid = false;
            
            if (errorsFound > 0)
            {
                Log("Please fix the errors above before using the system.");
            }
        }
        
        Log("=== End Validation ===");
    }
    
    private void Log(string message)
    {
        if (enableDetailedLogging)
        {
            Debug.Log($"[VehicleSystemValidator] {message}");
        }
    }
    
    private void LogWarning(string message)
    {
        warningsFound++;
        Debug.LogWarning($"[VehicleSystemValidator] WARNING: {message}");
    }
    
    private void LogError(string message)
    {
        errorsFound++;
        Debug.LogError($"[VehicleSystemValidator] ERROR: {message}");
    }
}
