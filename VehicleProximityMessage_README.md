# Vehicle Proximity Message System

## Overview
The Vehicle Proximity Message System replaces the old billboard UI with a simple on-screen text message that appears when the player is near a vehicle they can switch to.

## Features
- **Simple Text Message**: Shows clear instructions when near a selectable vehicle
- **Multi-Input Support**: Works with mouse, keyboard, and gamepad
- **Automatic Setup**: Easy one-click setup process
- **Customizable**: Configurable message position, text, colors, and buttons
- **Performance Optimized**: Lightweight compared to the old billboard system

## Quick Setup

### Method 1: Automatic Setup (Recommended)
1. **Add the setup script to your scene:**
   - Create an empty GameObject
   - Add the `VehicleProximityMessageSetup` component
   - The system will auto-setup when you start the game

2. **Manual setup trigger:**
   - Right-click on the `VehicleProximityMessageSetup` component
   - Select "Setup Proximity Message System" from the context menu

### Method 2: Manual Setup
1. **Create the message system:**
   - Create an empty GameObject named "VehicleProximityMessage"
   - Add the `JrsVehicleProximityMessage` component

2. **Update your JrsVehicleSelector:**
   - Find your `JrsVehicleSelector` in the scene
   - Drag the `VehicleProximityMessage` GameObject to the "Proximity Message" field
   - Ensure "Enable Proximity Selection" is checked

## How It Works

### User Experience
1. **Drive near another vehicle** (within 10 meters by default)
2. **See the message appear** on screen: "Press Left-Click or E or A Button to enter [Vehicle Name]"
3. **Press the indicated button** to switch to that vehicle
4. **Message disappears** when you move away or switch vehicles

### Input Controls
- **Mouse**: Left-Click
- **Keyboard**: E key
- **Gamepad**: A Button (Xbox) / X Button (PlayStation)

## Configuration

### Message Appearance
```csharp
// Change message position (offset from screen center)
proximityMessage.SetMessagePosition(new Vector2(0, 150));

// Change button text
proximityMessage.SetButtonTexts("Click", "Press A");

// Change font size and color
proximityMessage.SetFontSize(28);
proximityMessage.SetTextColor(Color.yellow);
```

### Detection Settings
```csharp
// Change detection range (in meters)
proximitySelector.DetectionRange = 15f;
```

## Troubleshooting

### Message Not Appearing
1. **Check system setup:**
   - Use the `VehicleProximityMessageSetup` component
   - Run "Test System" from the context menu

2. **Verify components:**
   - Ensure `JrsVehicleSelector` has proximity selection enabled
   - Check that `JrsVehicleProximitySelector` is in the scene
   - Confirm `JrsVehicleProximityMessage` is properly referenced

3. **Check vehicle setup:**
   - Vehicles must have `JrsVehicleController` component
   - Vehicles must be in the `JrsVehicleSelector.vehicles` list
   - Vehicles need colliders for proximity detection

### Input Not Working
1. **Check input system:**
   - Ensure `JrsNewInputController` is in the scene
   - Verify input actions are properly configured
   - Test with different input methods (mouse, keyboard, gamepad)

2. **Check proximity conditions:**
   - Must be within detection range (default 10m)
   - Cannot select the currently active vehicle
   - Target vehicle must be in the vehicles list

## Removing Old Billboard System

If you were using the old billboard system, you can clean it up:

1. **Use the cleanup tool:**
   - Right-click on `VehicleProximityMessageSetup`
   - Select "Remove Old Billboard System"

2. **Manual cleanup:**
   - Delete any `VehicleSelectionBillboard` GameObjects
   - Remove `JrsVehicleSelectionBillboard` references from `JrsVehicleSelector`
   - Delete the `select_to_enter.png` file if no longer needed

## Technical Details

### Components
- **JrsVehicleProximityMessage**: Main message display system
- **JrsVehicleProximitySelector**: Vehicle detection and selection logic
- **VehicleProximityMessageSetup**: Setup and configuration helper

### Performance
- Uses efficient proximity detection with configurable update intervals
- Lightweight UI system with minimal draw calls
- Automatic cleanup and memory management

### Events
The system uses events for communication:
```csharp
proximitySelector.OnNearestVehicleChanged += OnVehicleChanged;
```

## Customization Examples

### Change Message Format
```csharp
// In JrsVehicleProximityMessage.ShowMessage()
string message = $"Switch to {vehicleName}? Press {mouseButtonText} or {gamepadButtonText}";
```

### Add Sound Effects
```csharp
// Subscribe to vehicle change events
proximitySelector.OnNearestVehicleChanged += (vehicle) => {
    if (vehicle != null) {
        audioSource.PlayOneShot(proximitySound);
    }
};
```

### Multiple Message Positions
```csharp
// Different positions for different situations
if (isInCombat) {
    proximityMessage.SetMessagePosition(new Vector2(0, -100)); // Bottom center
} else {
    proximityMessage.SetMessagePosition(new Vector2(0, 100));  // Top center
}
```

## Support
If you encounter issues:
1. Check the Unity Console for error messages
2. Use the "Test System" function in `VehicleProximityMessageSetup`
3. Ensure all required components are present and properly configured
4. Verify that your vehicles have the necessary components and are in the vehicles list
