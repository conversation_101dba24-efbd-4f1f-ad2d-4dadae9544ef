using UnityEngine;
using UnityEngine.UI;

public class VehicleProximityDebugger : MonoBehaviour
{
    [Header("Debug Settings")]
    public bool enableDebugUI = true;
    public KeyCode debugKey = KeyCode.F1;
    
    [Header("References (Auto-found if null)")]
    public JrsVehicleSelector vehicleSelector;
    public JrsVehicleProximitySelector proximitySelector;
    public JrsVehicleSelectionBillboard selectionBillboard;
    
    // Debug UI
    private Canvas debugCanvas;
    private Text debugText;
    private bool debugUIVisible = false;
    
    void Start()
    {
        // Auto-find references
        if (vehicleSelector == null)
            vehicleSelector = FindObjectOfType<JrsVehicleSelector>();
        if (proximitySelector == null)
            proximitySelector = FindObjectOfType<JrsVehicleProximitySelector>();
        if (selectionBillboard == null)
            selectionBillboard = FindObjectOfType<JrsVehicleSelectionBillboard>();
            
        if (enableDebugUI)
        {
            CreateDebugUI();
        }
        
        // Log initial state
        LogSystemState();
    }
    
    void Update()
    {
        if (Input.GetKeyDown(debugKey))
        {
            ToggleDebugUI();
        }
        
        if (debugUIVisible && debugText != null)
        {
            UpdateDebugText();
        }
    }
    
    private void CreateDebugUI()
    {
        // Create debug canvas
        GameObject canvasObj = new GameObject("VehicleProximityDebugCanvas");
        debugCanvas = canvasObj.AddComponent<Canvas>();
        debugCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
        debugCanvas.sortingOrder = 1000;
        
        // Add CanvasScaler
        var scaler = canvasObj.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        
        // Add GraphicRaycaster
        canvasObj.AddComponent<GraphicRaycaster>();
        
        // Create background panel
        GameObject panelObj = new GameObject("DebugPanel");
        panelObj.transform.SetParent(debugCanvas.transform, false);
        
        var panelImage = panelObj.AddComponent<Image>();
        panelImage.color = new Color(0, 0, 0, 0.8f);
        
        var panelRect = panelObj.GetComponent<RectTransform>();
        panelRect.anchorMin = new Vector2(0, 0.5f);
        panelRect.anchorMax = new Vector2(0.4f, 1f);
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;
        
        // Create debug text
        GameObject textObj = new GameObject("DebugText");
        textObj.transform.SetParent(panelObj.transform, false);
        
        debugText = textObj.AddComponent<Text>();
        debugText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        debugText.fontSize = 14;
        debugText.color = Color.white;
        debugText.alignment = TextAnchor.UpperLeft;
        
        var textRect = textObj.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = new Vector2(10, 10);
        textRect.offsetMax = new Vector2(-10, -10);
        
        // Initially hide debug UI
        debugCanvas.gameObject.SetActive(false);
    }
    
    private void ToggleDebugUI()
    {
        if (debugCanvas != null)
        {
            debugUIVisible = !debugUIVisible;
            debugCanvas.gameObject.SetActive(debugUIVisible);
            
            if (debugUIVisible)
            {
                LogSystemState();
            }
        }
    }
    
    private void UpdateDebugText()
    {
        if (debugText == null) return;
        
        var currentVehicle = vehicleSelector?.GetCurrentVehicle();
        var billboard = selectionBillboard;
        
        string debugInfo = $"VEHICLE PROXIMITY DEBUG (Press {debugKey} to toggle)\n\n";
        
        // System status
        debugInfo += "=== SYSTEM STATUS ===\n";
        debugInfo += $"Vehicle Selector: {(vehicleSelector != null ? "✓" : "✗")}\n";
        debugInfo += $"Proximity Selector: {(proximitySelector != null ? "✓" : "✗")}\n";
        debugInfo += $"Selection Billboard: {(selectionBillboard != null ? "✓" : "✗")}\n";
        debugInfo += $"Proximity Selection Enabled: {(vehicleSelector?.enableProximitySelection ?? false)}\n\n";
        
        // Current vehicle info
        debugInfo += "=== CURRENT VEHICLE ===\n";
        debugInfo += $"Name: {(currentVehicle?.name ?? "None")}\n";
        debugInfo += $"Position: {(currentVehicle?.transform.position.ToString("F1") ?? "N/A")}\n\n";
        
        // Proximity detection info
        if (proximitySelector != null)
        {
            debugInfo += "=== PROXIMITY DETECTION ===\n";
            debugInfo += $"Detection Range: {proximitySelector.DetectionRange:F1}m\n";
            debugInfo += $"Can Select Nearest: {proximitySelector.CanSelectNearestVehicle()}\n";
            debugInfo += $"Nearby Vehicles: {proximitySelector.GetNearbyVehicleCount()}\n";
            
            var nearest = proximitySelector.GetNearestSelectableVehicle();
            if (nearest != null && currentVehicle != null)
            {
                float distance = Vector3.Distance(currentVehicle.transform.position, nearest.transform.position);
                debugInfo += $"Nearest Vehicle: {nearest.name}\n";
                debugInfo += $"Distance: {distance:F1}m\n";
            }
            else
            {
                debugInfo += "Nearest Vehicle: None\n";
            }
        }
        
        debugInfo += "\n=== BILLBOARD STATUS ===\n";
        if (billboard != null)
        {
            debugInfo += $"Billboard Visible: {billboard.IsVisible}\n";
            debugInfo += $"Target Vehicle: {(billboard.TargetVehicle?.name ?? "None")}\n";
            debugInfo += $"GameObject Active: {billboard.gameObject.activeInHierarchy}\n";
            
            var canvasGroup = billboard.GetComponent<CanvasGroup>();
            if (canvasGroup != null)
            {
                debugInfo += $"Canvas Alpha: {canvasGroup.alpha:F2}\n";
                debugInfo += $"Canvas Interactable: {canvasGroup.interactable}\n";
            }
        }
        else
        {
            debugInfo += "Billboard: Not found\n";
        }
        
        debugInfo += "\n=== CONTROLS ===\n";
        debugInfo += "• Drive near another vehicle\n";
        debugInfo += "• Gamepad: SELECT button to switch\n";
        debugInfo += "• Mouse: Click billboard/vehicle\n";
        debugInfo += "• Keyboard: Page Up/Down\n";
        
        debugText.text = debugInfo;
    }
    
    private void LogSystemState()
    {
        Debug.Log("=== VEHICLE PROXIMITY SYSTEM DEBUG ===");
        Debug.Log($"Vehicle Selector found: {vehicleSelector != null}");
        Debug.Log($"Proximity Selector found: {proximitySelector != null}");
        Debug.Log($"Selection Billboard found: {selectionBillboard != null}");
        
        if (vehicleSelector != null)
        {
            Debug.Log($"Proximity selection enabled: {vehicleSelector.enableProximitySelection}");
            Debug.Log($"Vehicle count: {vehicleSelector.vehicles?.Count ?? 0}");
        }
        
        if (selectionBillboard != null)
        {
            Debug.Log($"Billboard GameObject active: {selectionBillboard.gameObject.activeInHierarchy}");
            Debug.Log($"Billboard visible: {selectionBillboard.IsVisible}");
        }
    }
}
