//------------------------------------------------------------------------------
// <auto-generated>
//     This code was auto-generated by com.unity.inputsystem:InputActionCodeGenerator
//     version 1.7.0
//     from Assets/Police Pursuit Vehicle/Script/VehicleInputActions.inputactions
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine.InputSystem;
using UnityEngine.InputSystem.Utilities;

public partial class @VehicleInputActions: IInputActionCollection2, IDisposable
{
    public InputActionAsset asset { get; }
    public @VehicleInputActions()
    {
        asset = InputActionAsset.FromJson(@"{
    ""name"": ""VehicleInputActions"",
    ""maps"": [
        {
            ""name"": ""Vehicle"",
            ""id"": ""a8b5c2d1-e3f4-5678-9abc-def012345678"",
            ""actions"": [
                {
                    ""name"": ""Accelerate"",
                    ""type"": ""Button"",
                    ""id"": ""b1c2d3e4-f5a6-7890-bcde-f123456789ab"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""Brake"",
                    ""type"": ""Button"",
                    ""id"": ""c2d3e4f5-a6b7-8901-cdef-23456789abcd"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""Reverse"",
                    ""type"": ""Button"",
                    ""id"": ""d3e4f5a6-b7c8-9012-defa-3456789abcde"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""Steer"",
                    ""type"": ""Value"",
                    ""id"": ""e4f5a6b7-c8d9-0123-efab-456789abcdef"",
                    ""expectedControlType"": ""Axis"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": true
                },
                {
                    ""name"": ""HandBrake"",
                    ""type"": ""Button"",
                    ""id"": ""f5a6b7c8-d9e0-1234-fabc-56789abcdef0"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""HeadLights"",
                    ""type"": ""Button"",
                    ""id"": ""a6b7c8d9-e0f1-2345-abcd-6789abcdef01"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""Siren"",
                    ""type"": ""Button"",
                    ""id"": ""b7c8d9e0-f1a2-3456-bcde-789abcdef012"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""SignalLights"",
                    ""type"": ""Button"",
                    ""id"": ""c8d9e0f1-a2b3-4567-cdef-89abcdef0123"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""ExtraLights"",
                    ""type"": ""Button"",
                    ""id"": ""d9e0f1a2-b3c4-5678-defa-9abcdef01234"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""SwitchCamera"",
                    ""type"": ""Button"",
                    ""id"": ""e0f1a2b3-c4d5-6789-efab-abcdef012345"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""DoorToggle"",
                    ""type"": ""Button"",
                    ""id"": ""f1a2b3c4-d5e6-789a-fabc-def012345678"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""Door1Toggle"",
                    ""type"": ""Button"",
                    ""id"": ""a1b2c3d4-e5f6-789a-bcde-f012345678ab"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""Door2Toggle"",
                    ""type"": ""Button"",
                    ""id"": ""b2c3d4e5-f6a7-890b-cdef-0123456789bc"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""Door3Toggle"",
                    ""type"": ""Button"",
                    ""id"": ""c3d4e5f6-a7b8-901c-defa-123456789bcd"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""Door4Toggle"",
                    ""type"": ""Button"",
                    ""id"": ""d4e5f6a7-b8c9-012d-efab-23456789bcde"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""Door5Toggle"",
                    ""type"": ""Button"",
                    ""id"": ""e5f6a7b8-c9d0-123e-fabc-3456789bcdef"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""Door6Toggle"",
                    ""type"": ""Button"",
                    ""id"": ""f6a7b8c9-d0e1-234f-abcd-456789bcdef0"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""OrbitCameraRotate"",
                    ""type"": ""Value"",
                    ""id"": ""a1b2c3d4-e5f6-7890-abcd-ef0123456789"",
                    ""expectedControlType"": ""Vector2"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": true
                },
                {
                    ""name"": ""OrbitCameraZoom"",
                    ""type"": ""Value"",
                    ""id"": ""b2c3d4e5-f6a7-8901-bcde-f01234567890"",
                    ""expectedControlType"": ""Axis"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": true
                },
                {
                    ""name"": ""VehicleSelect"",
                    ""type"": ""Button"",
                    ""id"": ""c3d4e5f6-a7b8-9012-cdef-012345678901"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""HelpToggle"",
                    ""type"": ""Button"",
                    ""id"": ""d4e5f6a7-b8c9-0123-defa-123456789012"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                }
            ],
            ""bindings"": [
                {
                    ""name"": """",
                    ""id"": ""f1a2b3c4-d5e6-789a-fabc-bcdef0123456"",
                    ""path"": ""<Keyboard>/w"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""Accelerate"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""a2b3c4d5-e6f7-89ab-abcd-cdef01234567"",
                    ""path"": ""<Keyboard>/upArrow"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""Accelerate"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""733b5cac-d490-47b1-97f9-5e28ed33e9cd"",
                    ""path"": ""<Gamepad>/rightTrigger"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Accelerate"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""b3c4d5e6-f7a8-9abc-bcde-def012345678"",
                    ""path"": ""<Keyboard>/space"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""HandBrake"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""fddb621c-9e23-4963-a619-712efe254a5e"",
                    ""path"": ""<Gamepad>/buttonSouth"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""HandBrake"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""c4d5e6f7-a8b9-abcd-cdef-ef0123456789"",
                    ""path"": ""<Keyboard>/s"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""Reverse"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""d5e6f7a8-b9ca-bcde-defa-f01234567890"",
                    ""path"": ""<Keyboard>/downArrow"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""Reverse"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""41734a50-f4e7-46be-81ae-c7487dcdcc03"",
                    ""path"": ""<Gamepad>/leftTrigger"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Reverse"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": ""1D Axis"",
                    ""id"": ""e6f7a8b9-cadb-cdef-efab-012345678901"",
                    ""path"": ""1DAxis"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Steer"",
                    ""isComposite"": true,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": ""negative"",
                    ""id"": ""f7a8b9ca-dbec-defa-fabc-123456789012"",
                    ""path"": ""<Keyboard>/a"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""Steer"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": true
                },
                {
                    ""name"": ""positive"",
                    ""id"": ""a8b9cadb-ecfd-efab-abcd-234567890123"",
                    ""path"": ""<Keyboard>/d"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""Steer"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": true
                },
                {
                    ""name"": ""1D Axis"",
                    ""id"": ""b9cadbec-fdae-fabc-bcde-345678901234"",
                    ""path"": ""1DAxis"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Steer"",
                    ""isComposite"": true,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": ""negative"",
                    ""id"": ""cadbecfd-aefb-abcd-cdef-456789012345"",
                    ""path"": ""<Keyboard>/leftArrow"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""Steer"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": true
                },
                {
                    ""name"": ""positive"",
                    ""id"": ""dbecfdae-fbac-bcde-defa-567890123456"",
                    ""path"": ""<Keyboard>/rightArrow"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""Steer"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": true
                },
                {
                    ""name"": ""1D Axis"",
                    ""id"": ""4b1b7679-734e-4dc5-ba7e-abe014a54720"",
                    ""path"": ""1DAxis"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Steer"",
                    ""isComposite"": true,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": ""negative"",
                    ""id"": ""8f2908ec-9bc4-4bde-b9b2-ab17a689d874"",
                    ""path"": ""<Gamepad>/leftStick/left"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Steer"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": true
                },
                {
                    ""name"": ""positive"",
                    ""id"": ""aed7dc8f-2d9d-460b-a4e4-1dd874c84024"",
                    ""path"": ""<Gamepad>/leftStick/right"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Steer"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": true
                },
                {
                    ""name"": """",
                    ""id"": ""ecfdaefb-acbd-cdef-efab-678901234567"",
                    ""path"": ""<Keyboard>/h"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""HeadLights"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""7e7ce99b-58c6-4856-a5bc-20ad0a28d68d"",
                    ""path"": ""<Gamepad>/rightShoulder"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""HeadLights"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""fdaefbac-bdce-defa-fabc-789012345678"",
                    ""path"": ""<Keyboard>/p"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""Siren"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""7fa8ea03-748f-4330-9c3f-8d8444ba36b3"",
                    ""path"": ""<Gamepad>/buttonNorth"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Siren"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""aefbacbd-cedf-efab-abcd-890123456789"",
                    ""path"": ""<Keyboard>/t"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""SignalLights"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""5753a800-83b8-4443-8bf4-2a83cdf3194d"",
                    ""path"": ""<Gamepad>/leftShoulder"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""SignalLights"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""fbacbdce-dfea-fabc-bcde-901234567890"",
                    ""path"": ""<Keyboard>/e"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""ExtraLights"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""6679889e-2556-4af4-801e-944783590ef8"",
                    ""path"": ""<Gamepad>/buttonWest"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""ExtraLights"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""acbdcedf-eafb-abcd-cdef-012345678901"",
                    ""path"": ""<Keyboard>/#(C)"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""SwitchCamera"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""7adbab63-6e89-4179-85e6-f22962e4e66c"",
                    ""path"": ""<Gamepad>/buttonEast"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""SwitchCamera"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""1a79b056-adeb-4f0c-a9c7-c37784489373"",
                    ""path"": ""<Keyboard>/space"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""Brake"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""6bebdbc1-2599-4f86-8128-297a98031db5"",
                    ""path"": ""<Gamepad>/leftTrigger"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Brake"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""2b8ac167-befc-5f1d-b8d8-d48895590484"",
                    ""path"": ""<Keyboard>/o"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""DoorToggle"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""3c9bd278-cfed-6f2e-c9e9-e59906601595"",
                    ""path"": ""<Keyboard>/numpad4"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""Door1Toggle"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""4b703fe3-df52-408b-b8b9-07a1b870b039"",
                    ""path"": ""<Gamepad>/dpad/left"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Gamepad"",
                    ""action"": ""Door1Toggle"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""4d0ce389-d0fe-7f3f-d0f0-f60017712606"",
                    ""path"": ""<Keyboard>/numpad5"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""Door2Toggle"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""1c08192d-cced-41ad-9693-ba9eb0328f23"",
                    ""path"": ""<Gamepad>/dpad/right"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Gamepad"",
                    ""action"": ""Door2Toggle"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""5e1df490-e1af-8f4a-e1a1-a71128823717"",
                    ""path"": ""<Keyboard>/numpad1"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""Door3Toggle"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""6f2ea501-f2ba-9f5b-f2b2-b82239934828"",
                    ""path"": ""<Keyboard>/numpad2"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""Door4Toggle"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""7a3fb612-a3cb-0a6c-a3c3-c93340045939"",
                    ""path"": ""<Keyboard>/5"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""Door5Toggle"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""8b4ac723-b4dc-1b7d-b4d4-d04451156040"",
                    ""path"": ""<Keyboard>/6"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""Door6Toggle"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""c3d4e5f6-a7b8-9012-cdef-123456789abc"",
                    ""path"": ""<Mouse>/delta"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""OrbitCameraRotate"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""d4e5f6a7-b8c9-0123-defa-23456789abcd"",
                    ""path"": ""<Gamepad>/rightStick"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Gamepad"",
                    ""action"": ""OrbitCameraRotate"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""e5f6a7b8-c9d0-1234-efab-3456789abcde"",
                    ""path"": ""<Mouse>/scroll/y"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""OrbitCameraZoom"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": ""Zoom In/Out"",
                    ""id"": ""f6a7b8c9-d0e1-2345-fabc-456789abcdef"",
                    ""path"": ""1DAxis"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""OrbitCameraZoom"",
                    ""isComposite"": true,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": ""negative"",
                    ""id"": ""0af7d856-bae1-4419-a4c5-73c911bb4048"",
                    ""path"": ""<Gamepad>/dpad/down"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Gamepad"",
                    ""action"": ""OrbitCameraZoom"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": true
                },
                {
                    ""name"": ""positive"",
                    ""id"": ""b8c9d0e1-f2a3-4567-bcde-6789abcdef01"",
                    ""path"": ""<Gamepad>/dpad/up"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Gamepad"",
                    ""action"": ""OrbitCameraZoom"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": true
                },
                {
                    ""name"": """",
                    ""id"": ""d4e5f6a7-b8c9-0123-defa-23456789bcde"",
                    ""path"": ""<Keyboard>/e"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""VehicleSelect"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""e5f6a7b8-c9d0-1234-efab-3456789bcdef"",
                    ""path"": ""<Mouse>/leftButton"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""VehicleSelect"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""f6a7b8c9-d0e1-2345-fabc-456789bcdef0"",
                    ""path"": ""<Touchscreen>/primaryTouch/tap"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Touch"",
                    ""action"": ""VehicleSelect"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""a7b8c9d0-e1f2-3456-abcd-56789abcdef0"",
                    ""path"": ""<Gamepad>/select"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Gamepad"",
                    ""action"": ""VehicleSelect"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""e6f7a8b9-c0d1-2345-efab-6789abcdef01"",
                    ""path"": ""<Keyboard>/f1"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Keyboard"",
                    ""action"": ""HelpToggle"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""f7a8b9c0-d1e2-3456-fabc-789abcdef012"",
                    ""path"": ""<Gamepad>/start"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": ""Gamepad"",
                    ""action"": ""HelpToggle"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                }
            ]
        }
    ],
    ""controlSchemes"": [
        {
            ""name"": ""Keyboard"",
            ""bindingGroup"": ""Keyboard"",
            ""devices"": [
                {
                    ""devicePath"": ""<Keyboard>"",
                    ""isOptional"": false,
                    ""isOR"": false
                },
                {
                    ""devicePath"": ""<Mouse>"",
                    ""isOptional"": false,
                    ""isOR"": false
                }
            ]
        },
        {
            ""name"": ""Touch"",
            ""bindingGroup"": ""Touch"",
            ""devices"": [
                {
                    ""devicePath"": ""<Touchscreen>"",
                    ""isOptional"": false,
                    ""isOR"": false
                }
            ]
        },
        {
            ""name"": ""Gamepad"",
            ""bindingGroup"": ""Gamepad"",
            ""devices"": [
                {
                    ""devicePath"": ""<Gamepad>"",
                    ""isOptional"": false,
                    ""isOR"": false
                }
            ]
        }
    ]
}");
        // Vehicle
        m_Vehicle = asset.FindActionMap("Vehicle", throwIfNotFound: true);
        m_Vehicle_Accelerate = m_Vehicle.FindAction("Accelerate", throwIfNotFound: true);
        m_Vehicle_Brake = m_Vehicle.FindAction("Brake", throwIfNotFound: true);
        m_Vehicle_Reverse = m_Vehicle.FindAction("Reverse", throwIfNotFound: true);
        m_Vehicle_Steer = m_Vehicle.FindAction("Steer", throwIfNotFound: true);
        m_Vehicle_HandBrake = m_Vehicle.FindAction("HandBrake", throwIfNotFound: true);
        m_Vehicle_HeadLights = m_Vehicle.FindAction("HeadLights", throwIfNotFound: true);
        m_Vehicle_Siren = m_Vehicle.FindAction("Siren", throwIfNotFound: true);
        m_Vehicle_SignalLights = m_Vehicle.FindAction("SignalLights", throwIfNotFound: true);
        m_Vehicle_ExtraLights = m_Vehicle.FindAction("ExtraLights", throwIfNotFound: true);
        m_Vehicle_SwitchCamera = m_Vehicle.FindAction("SwitchCamera", throwIfNotFound: true);
        m_Vehicle_DoorToggle = m_Vehicle.FindAction("DoorToggle", throwIfNotFound: true);
        m_Vehicle_Door1Toggle = m_Vehicle.FindAction("Door1Toggle", throwIfNotFound: true);
        m_Vehicle_Door2Toggle = m_Vehicle.FindAction("Door2Toggle", throwIfNotFound: true);
        m_Vehicle_Door3Toggle = m_Vehicle.FindAction("Door3Toggle", throwIfNotFound: true);
        m_Vehicle_Door4Toggle = m_Vehicle.FindAction("Door4Toggle", throwIfNotFound: true);
        m_Vehicle_Door5Toggle = m_Vehicle.FindAction("Door5Toggle", throwIfNotFound: true);
        m_Vehicle_Door6Toggle = m_Vehicle.FindAction("Door6Toggle", throwIfNotFound: true);
        m_Vehicle_OrbitCameraRotate = m_Vehicle.FindAction("OrbitCameraRotate", throwIfNotFound: true);
        m_Vehicle_OrbitCameraZoom = m_Vehicle.FindAction("OrbitCameraZoom", throwIfNotFound: true);
        m_Vehicle_VehicleSelect = m_Vehicle.FindAction("VehicleSelect", throwIfNotFound: true);
        m_Vehicle_HelpToggle = m_Vehicle.FindAction("HelpToggle", throwIfNotFound: true);
    }

    public void Dispose()
    {
        UnityEngine.Object.Destroy(asset);
    }

    public InputBinding? bindingMask
    {
        get => asset.bindingMask;
        set => asset.bindingMask = value;
    }

    public ReadOnlyArray<InputDevice>? devices
    {
        get => asset.devices;
        set => asset.devices = value;
    }

    public ReadOnlyArray<InputControlScheme> controlSchemes => asset.controlSchemes;

    public bool Contains(InputAction action)
    {
        return asset.Contains(action);
    }

    public IEnumerator<InputAction> GetEnumerator()
    {
        return asset.GetEnumerator();
    }

    IEnumerator IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }

    public void Enable()
    {
        asset.Enable();
    }

    public void Disable()
    {
        asset.Disable();
    }

    public IEnumerable<InputBinding> bindings => asset.bindings;

    public InputAction FindAction(string actionNameOrId, bool throwIfNotFound = false)
    {
        return asset.FindAction(actionNameOrId, throwIfNotFound);
    }

    public int FindBinding(InputBinding bindingMask, out InputAction action)
    {
        return asset.FindBinding(bindingMask, out action);
    }

    // Vehicle
    private readonly InputActionMap m_Vehicle;
    private List<IVehicleActions> m_VehicleActionsCallbackInterfaces = new List<IVehicleActions>();
    private readonly InputAction m_Vehicle_Accelerate;
    private readonly InputAction m_Vehicle_Brake;
    private readonly InputAction m_Vehicle_Reverse;
    private readonly InputAction m_Vehicle_Steer;
    private readonly InputAction m_Vehicle_HandBrake;
    private readonly InputAction m_Vehicle_HeadLights;
    private readonly InputAction m_Vehicle_Siren;
    private readonly InputAction m_Vehicle_SignalLights;
    private readonly InputAction m_Vehicle_ExtraLights;
    private readonly InputAction m_Vehicle_SwitchCamera;
    private readonly InputAction m_Vehicle_DoorToggle;
    private readonly InputAction m_Vehicle_Door1Toggle;
    private readonly InputAction m_Vehicle_Door2Toggle;
    private readonly InputAction m_Vehicle_Door3Toggle;
    private readonly InputAction m_Vehicle_Door4Toggle;
    private readonly InputAction m_Vehicle_Door5Toggle;
    private readonly InputAction m_Vehicle_Door6Toggle;
    private readonly InputAction m_Vehicle_OrbitCameraRotate;
    private readonly InputAction m_Vehicle_OrbitCameraZoom;
    private readonly InputAction m_Vehicle_VehicleSelect;
    private readonly InputAction m_Vehicle_HelpToggle;
    public struct VehicleActions
    {
        private @VehicleInputActions m_Wrapper;
        public VehicleActions(@VehicleInputActions wrapper) { m_Wrapper = wrapper; }
        public InputAction @Accelerate => m_Wrapper.m_Vehicle_Accelerate;
        public InputAction @Brake => m_Wrapper.m_Vehicle_Brake;
        public InputAction @Reverse => m_Wrapper.m_Vehicle_Reverse;
        public InputAction @Steer => m_Wrapper.m_Vehicle_Steer;
        public InputAction @HandBrake => m_Wrapper.m_Vehicle_HandBrake;
        public InputAction @HeadLights => m_Wrapper.m_Vehicle_HeadLights;
        public InputAction @Siren => m_Wrapper.m_Vehicle_Siren;
        public InputAction @SignalLights => m_Wrapper.m_Vehicle_SignalLights;
        public InputAction @ExtraLights => m_Wrapper.m_Vehicle_ExtraLights;
        public InputAction @SwitchCamera => m_Wrapper.m_Vehicle_SwitchCamera;
        public InputAction @DoorToggle => m_Wrapper.m_Vehicle_DoorToggle;
        public InputAction @Door1Toggle => m_Wrapper.m_Vehicle_Door1Toggle;
        public InputAction @Door2Toggle => m_Wrapper.m_Vehicle_Door2Toggle;
        public InputAction @Door3Toggle => m_Wrapper.m_Vehicle_Door3Toggle;
        public InputAction @Door4Toggle => m_Wrapper.m_Vehicle_Door4Toggle;
        public InputAction @Door5Toggle => m_Wrapper.m_Vehicle_Door5Toggle;
        public InputAction @Door6Toggle => m_Wrapper.m_Vehicle_Door6Toggle;
        public InputAction @OrbitCameraRotate => m_Wrapper.m_Vehicle_OrbitCameraRotate;
        public InputAction @OrbitCameraZoom => m_Wrapper.m_Vehicle_OrbitCameraZoom;
        public InputAction @VehicleSelect => m_Wrapper.m_Vehicle_VehicleSelect;
        public InputAction @HelpToggle => m_Wrapper.m_Vehicle_HelpToggle;
        public InputActionMap Get() { return m_Wrapper.m_Vehicle; }
        public void Enable() { Get().Enable(); }
        public void Disable() { Get().Disable(); }
        public bool enabled => Get().enabled;
        public static implicit operator InputActionMap(VehicleActions set) { return set.Get(); }
        public void AddCallbacks(IVehicleActions instance)
        {
            if (instance == null || m_Wrapper.m_VehicleActionsCallbackInterfaces.Contains(instance)) return;
            m_Wrapper.m_VehicleActionsCallbackInterfaces.Add(instance);
            @Accelerate.started += instance.OnAccelerate;
            @Accelerate.performed += instance.OnAccelerate;
            @Accelerate.canceled += instance.OnAccelerate;
            @Brake.started += instance.OnBrake;
            @Brake.performed += instance.OnBrake;
            @Brake.canceled += instance.OnBrake;
            @Reverse.started += instance.OnReverse;
            @Reverse.performed += instance.OnReverse;
            @Reverse.canceled += instance.OnReverse;
            @Steer.started += instance.OnSteer;
            @Steer.performed += instance.OnSteer;
            @Steer.canceled += instance.OnSteer;
            @HandBrake.started += instance.OnHandBrake;
            @HandBrake.performed += instance.OnHandBrake;
            @HandBrake.canceled += instance.OnHandBrake;
            @HeadLights.started += instance.OnHeadLights;
            @HeadLights.performed += instance.OnHeadLights;
            @HeadLights.canceled += instance.OnHeadLights;
            @Siren.started += instance.OnSiren;
            @Siren.performed += instance.OnSiren;
            @Siren.canceled += instance.OnSiren;
            @SignalLights.started += instance.OnSignalLights;
            @SignalLights.performed += instance.OnSignalLights;
            @SignalLights.canceled += instance.OnSignalLights;
            @ExtraLights.started += instance.OnExtraLights;
            @ExtraLights.performed += instance.OnExtraLights;
            @ExtraLights.canceled += instance.OnExtraLights;
            @SwitchCamera.started += instance.OnSwitchCamera;
            @SwitchCamera.performed += instance.OnSwitchCamera;
            @SwitchCamera.canceled += instance.OnSwitchCamera;
            @DoorToggle.started += instance.OnDoorToggle;
            @DoorToggle.performed += instance.OnDoorToggle;
            @DoorToggle.canceled += instance.OnDoorToggle;
            @Door1Toggle.started += instance.OnDoor1Toggle;
            @Door1Toggle.performed += instance.OnDoor1Toggle;
            @Door1Toggle.canceled += instance.OnDoor1Toggle;
            @Door2Toggle.started += instance.OnDoor2Toggle;
            @Door2Toggle.performed += instance.OnDoor2Toggle;
            @Door2Toggle.canceled += instance.OnDoor2Toggle;
            @Door3Toggle.started += instance.OnDoor3Toggle;
            @Door3Toggle.performed += instance.OnDoor3Toggle;
            @Door3Toggle.canceled += instance.OnDoor3Toggle;
            @Door4Toggle.started += instance.OnDoor4Toggle;
            @Door4Toggle.performed += instance.OnDoor4Toggle;
            @Door4Toggle.canceled += instance.OnDoor4Toggle;
            @Door5Toggle.started += instance.OnDoor5Toggle;
            @Door5Toggle.performed += instance.OnDoor5Toggle;
            @Door5Toggle.canceled += instance.OnDoor5Toggle;
            @Door6Toggle.started += instance.OnDoor6Toggle;
            @Door6Toggle.performed += instance.OnDoor6Toggle;
            @Door6Toggle.canceled += instance.OnDoor6Toggle;
            @OrbitCameraRotate.started += instance.OnOrbitCameraRotate;
            @OrbitCameraRotate.performed += instance.OnOrbitCameraRotate;
            @OrbitCameraRotate.canceled += instance.OnOrbitCameraRotate;
            @OrbitCameraZoom.started += instance.OnOrbitCameraZoom;
            @OrbitCameraZoom.performed += instance.OnOrbitCameraZoom;
            @OrbitCameraZoom.canceled += instance.OnOrbitCameraZoom;
            @VehicleSelect.started += instance.OnVehicleSelect;
            @VehicleSelect.performed += instance.OnVehicleSelect;
            @VehicleSelect.canceled += instance.OnVehicleSelect;
            @HelpToggle.started += instance.OnHelpToggle;
            @HelpToggle.performed += instance.OnHelpToggle;
            @HelpToggle.canceled += instance.OnHelpToggle;
        }

        private void UnregisterCallbacks(IVehicleActions instance)
        {
            @Accelerate.started -= instance.OnAccelerate;
            @Accelerate.performed -= instance.OnAccelerate;
            @Accelerate.canceled -= instance.OnAccelerate;
            @Brake.started -= instance.OnBrake;
            @Brake.performed -= instance.OnBrake;
            @Brake.canceled -= instance.OnBrake;
            @Reverse.started -= instance.OnReverse;
            @Reverse.performed -= instance.OnReverse;
            @Reverse.canceled -= instance.OnReverse;
            @Steer.started -= instance.OnSteer;
            @Steer.performed -= instance.OnSteer;
            @Steer.canceled -= instance.OnSteer;
            @HandBrake.started -= instance.OnHandBrake;
            @HandBrake.performed -= instance.OnHandBrake;
            @HandBrake.canceled -= instance.OnHandBrake;
            @HeadLights.started -= instance.OnHeadLights;
            @HeadLights.performed -= instance.OnHeadLights;
            @HeadLights.canceled -= instance.OnHeadLights;
            @Siren.started -= instance.OnSiren;
            @Siren.performed -= instance.OnSiren;
            @Siren.canceled -= instance.OnSiren;
            @SignalLights.started -= instance.OnSignalLights;
            @SignalLights.performed -= instance.OnSignalLights;
            @SignalLights.canceled -= instance.OnSignalLights;
            @ExtraLights.started -= instance.OnExtraLights;
            @ExtraLights.performed -= instance.OnExtraLights;
            @ExtraLights.canceled -= instance.OnExtraLights;
            @SwitchCamera.started -= instance.OnSwitchCamera;
            @SwitchCamera.performed -= instance.OnSwitchCamera;
            @SwitchCamera.canceled -= instance.OnSwitchCamera;
            @DoorToggle.started -= instance.OnDoorToggle;
            @DoorToggle.performed -= instance.OnDoorToggle;
            @DoorToggle.canceled -= instance.OnDoorToggle;
            @Door1Toggle.started -= instance.OnDoor1Toggle;
            @Door1Toggle.performed -= instance.OnDoor1Toggle;
            @Door1Toggle.canceled -= instance.OnDoor1Toggle;
            @Door2Toggle.started -= instance.OnDoor2Toggle;
            @Door2Toggle.performed -= instance.OnDoor2Toggle;
            @Door2Toggle.canceled -= instance.OnDoor2Toggle;
            @Door3Toggle.started -= instance.OnDoor3Toggle;
            @Door3Toggle.performed -= instance.OnDoor3Toggle;
            @Door3Toggle.canceled -= instance.OnDoor3Toggle;
            @Door4Toggle.started -= instance.OnDoor4Toggle;
            @Door4Toggle.performed -= instance.OnDoor4Toggle;
            @Door4Toggle.canceled -= instance.OnDoor4Toggle;
            @Door5Toggle.started -= instance.OnDoor5Toggle;
            @Door5Toggle.performed -= instance.OnDoor5Toggle;
            @Door5Toggle.canceled -= instance.OnDoor5Toggle;
            @Door6Toggle.started -= instance.OnDoor6Toggle;
            @Door6Toggle.performed -= instance.OnDoor6Toggle;
            @Door6Toggle.canceled -= instance.OnDoor6Toggle;
            @OrbitCameraRotate.started -= instance.OnOrbitCameraRotate;
            @OrbitCameraRotate.performed -= instance.OnOrbitCameraRotate;
            @OrbitCameraRotate.canceled -= instance.OnOrbitCameraRotate;
            @OrbitCameraZoom.started -= instance.OnOrbitCameraZoom;
            @OrbitCameraZoom.performed -= instance.OnOrbitCameraZoom;
            @OrbitCameraZoom.canceled -= instance.OnOrbitCameraZoom;
            @VehicleSelect.started -= instance.OnVehicleSelect;
            @VehicleSelect.performed -= instance.OnVehicleSelect;
            @VehicleSelect.canceled -= instance.OnVehicleSelect;
            @HelpToggle.started -= instance.OnHelpToggle;
            @HelpToggle.performed -= instance.OnHelpToggle;
            @HelpToggle.canceled -= instance.OnHelpToggle;
        }

        public void RemoveCallbacks(IVehicleActions instance)
        {
            if (m_Wrapper.m_VehicleActionsCallbackInterfaces.Remove(instance))
                UnregisterCallbacks(instance);
        }

        public void SetCallbacks(IVehicleActions instance)
        {
            foreach (var item in m_Wrapper.m_VehicleActionsCallbackInterfaces)
                UnregisterCallbacks(item);
            m_Wrapper.m_VehicleActionsCallbackInterfaces.Clear();
            AddCallbacks(instance);
        }
    }
    public VehicleActions @Vehicle => new VehicleActions(this);
    private int m_KeyboardSchemeIndex = -1;
    public InputControlScheme KeyboardScheme
    {
        get
        {
            if (m_KeyboardSchemeIndex == -1) m_KeyboardSchemeIndex = asset.FindControlSchemeIndex("Keyboard");
            return asset.controlSchemes[m_KeyboardSchemeIndex];
        }
    }
    private int m_TouchSchemeIndex = -1;
    public InputControlScheme TouchScheme
    {
        get
        {
            if (m_TouchSchemeIndex == -1) m_TouchSchemeIndex = asset.FindControlSchemeIndex("Touch");
            return asset.controlSchemes[m_TouchSchemeIndex];
        }
    }
    private int m_GamepadSchemeIndex = -1;
    public InputControlScheme GamepadScheme
    {
        get
        {
            if (m_GamepadSchemeIndex == -1) m_GamepadSchemeIndex = asset.FindControlSchemeIndex("Gamepad");
            return asset.controlSchemes[m_GamepadSchemeIndex];
        }
    }
    public interface IVehicleActions
    {
        void OnAccelerate(InputAction.CallbackContext context);
        void OnBrake(InputAction.CallbackContext context);
        void OnReverse(InputAction.CallbackContext context);
        void OnSteer(InputAction.CallbackContext context);
        void OnHandBrake(InputAction.CallbackContext context);
        void OnHeadLights(InputAction.CallbackContext context);
        void OnSiren(InputAction.CallbackContext context);
        void OnSignalLights(InputAction.CallbackContext context);
        void OnExtraLights(InputAction.CallbackContext context);
        void OnSwitchCamera(InputAction.CallbackContext context);
        void OnDoorToggle(InputAction.CallbackContext context);
        void OnDoor1Toggle(InputAction.CallbackContext context);
        void OnDoor2Toggle(InputAction.CallbackContext context);
        void OnDoor3Toggle(InputAction.CallbackContext context);
        void OnDoor4Toggle(InputAction.CallbackContext context);
        void OnDoor5Toggle(InputAction.CallbackContext context);
        void OnDoor6Toggle(InputAction.CallbackContext context);
        void OnOrbitCameraRotate(InputAction.CallbackContext context);
        void OnOrbitCameraZoom(InputAction.CallbackContext context);
        void OnVehicleSelect(InputAction.CallbackContext context);
        void OnHelpToggle(InputAction.CallbackContext context);
    }
}
