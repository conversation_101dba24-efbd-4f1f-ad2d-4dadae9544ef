using UnityEngine;
using UnityEngine.InputSystem;

public class JrsToggleObject : MonoBehaviour
{
    [Header("Toggle Settings")]
    public GameObject objectToToggle;

    [Header("Legacy Input (Fallback)")]
    public KeyCode toggleKey = KeyCode.F1;

    [Header("New Input System")]
    public bool useNewInputSystem = true;

    private VehicleInputActions vehicleInputActions;
    private InputAction helpToggleAction;
    private JrsNewInputController inputController;

    void Start()
    {
        // Try to find the input controller in the scene
        inputController = FindObjectOfType<JrsNewInputController>();

        if (useNewInputSystem)
        {
            // Initialize the input actions
            vehicleInputActions = new VehicleInputActions();
            helpToggleAction = vehicleInputActions.Vehicle.HelpToggle;

            // Subscribe to the action
            helpToggleAction.performed += OnHelpTogglePressed;

            // Enable the action
            vehicleInputActions.Enable();
        }
    }

    void Update()
    {
        // Fallback to legacy input if New Input System is not used or not available
        if (!useNewInputSystem || vehicleInputActions == null)
        {
            if (Input.GetKeyDown(toggleKey))
            {
                ToggleObject();
            }
        }

        // Also check mobile input if available
        if (inputController != null && inputController.GetHelpToggleInput())
        {
            ToggleObject();
        }
    }

    private void OnHelpTogglePressed(InputAction.CallbackContext context)
    {
        ToggleObject();
    }

    private void ToggleObject()
    {
        if (objectToToggle != null)
        {
            objectToToggle.SetActive(!objectToToggle.activeSelf);
        }
    }

    void OnDestroy()
    {
        if (vehicleInputActions != null)
        {
            helpToggleAction.performed -= OnHelpTogglePressed;
            vehicleInputActions.Dispose();
        }
    }
}

