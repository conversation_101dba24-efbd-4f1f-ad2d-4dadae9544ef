using UnityEngine;
using UnityEngine.InputSystem;

public class JrsToggleObject : MonoBehaviour
{
    [Header("Toggle Settings")]
    public GameObject objectToToggle;

    [Header("Legacy Input (Fallback)")]
    public KeyCode toggleKey = KeyCode.F1;

    [Header("New Input System")]
    public bool useNewInputSystem = true;

    private JrsNewInputController inputController;
    private JrsVehicleController vehicleController;
    private bool wasHelpPressed = false;

    void Start()
    {
        // Try to find the input controller and vehicle controller in the scene
        inputController = FindObjectOfType<JrsNewInputController>();
        vehicleController = FindObjectOfType<JrsVehicleController>();

        // Auto-detect input system from vehicle controller if available
        if (vehicleController != null)
        {
            useNewInputSystem = vehicleController.useNewInputSystem;
            Debug.Log($"JrsToggleObject: Auto-detected input system from vehicle controller: {(useNewInputSystem ? "New Input System" : "Legacy Input System")}");
        }

        if (inputController == null && useNewInputSystem)
        {
            Debug.LogWarning("JrsToggleObject: JrsNewInputController not found in scene. Falling back to legacy input.");
            useNewInputSystem = false;
        }

        Debug.Log($"JrsToggleObject: Using {(useNewInputSystem ? "New Input System" : "Legacy Input System")} for help toggle (F1 key)");
    }

    void Update()
    {
        bool helpPressed = false;

        // Check New Input System first (through the existing input controller)
        if (useNewInputSystem && inputController != null)
        {
            helpPressed = inputController.GetHelpToggleInput();
        }

        // Fallback to legacy input if New Input System is not used or not available
        if (!useNewInputSystem)
        {
            helpPressed = Input.GetKeyDown(toggleKey);
        }

        // Toggle on button press (not hold)
        if (helpPressed && !wasHelpPressed)
        {
            ToggleObject();
        }

        wasHelpPressed = helpPressed;
    }

    private void ToggleObject()
    {
        if (objectToToggle != null)
        {
            bool newState = !objectToToggle.activeSelf;
            objectToToggle.SetActive(newState);
            Debug.Log($"JrsToggleObject: Toggled {objectToToggle.name} to {(newState ? "VISIBLE" : "HIDDEN")}");
        }
        else
        {
            Debug.LogWarning("JrsToggleObject: objectToToggle is null! Please assign the object you want to toggle.");
        }
    }
}

