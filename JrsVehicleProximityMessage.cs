using UnityEngine;
using UnityEngine.UI;
using System.Linq;

public class JrsVehicleProximityMessage : MonoBehaviour
{
    [Header("Message Settings")]
    [SerializeField] private float messageDuration = 0.5f;
    [SerializeField] private float fadeSpeed = 2f;
    [SerializeField] private Vector2 messagePosition = new Vector2(0, 100); // Offset from center
    [SerializeField] private Vector2 messageSize = new Vector2(300, 100); // Size of the message image

    [Header("Message Images")]
    [SerializeField] private Sprite defaultMessageSprite; // Default message image
    [SerializeField] private Sprite customMessageSprite; // Custom message image (optional)

    [Header("UI References")]
    [SerializeField] private Canvas messageCanvas;
    [SerializeField] private Image messageImage;
    [SerializeField] private CanvasGroup canvasGroup;
    
    // Private variables
    private JrsVehicleProximitySelector proximitySelector;
    private JrsVehicleController nearestVehicle;
    private bool isMessageVisible = false;
    private float messageTimer = 0f;
    private float targetAlpha = 0f;
    
    void Awake()
    {
        CreateMessageUI();
    }
    
    void Start()
    {
        Initialize();
    }
    
    void Update()
    {
        UpdateMessageVisibility();
        UpdateMessageFade();
    }
    
    private void CreateMessageUI()
    {
        // Create canvas if not assigned
        if (messageCanvas == null)
        {
            GameObject canvasObj = new GameObject("ProximityMessageCanvas");
            canvasObj.transform.SetParent(transform);
            
            messageCanvas = canvasObj.AddComponent<Canvas>();
            messageCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
            messageCanvas.sortingOrder = 100; // High priority to appear on top
            
            // Add CanvasScaler for responsive UI
            var canvasScaler = canvasObj.AddComponent<CanvasScaler>();
            canvasScaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            canvasScaler.referenceResolution = new Vector2(1920, 1080);
            canvasScaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
            canvasScaler.matchWidthOrHeight = 0.5f;
            
            // Add GraphicRaycaster for UI interactions
            canvasObj.AddComponent<GraphicRaycaster>();
        }
        
        // Create canvas group for fade effects
        if (canvasGroup == null)
        {
            canvasGroup = messageCanvas.gameObject.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
            {
                canvasGroup = messageCanvas.gameObject.AddComponent<CanvasGroup>();
            }
        }
        
        // Create message image if not assigned
        if (messageImage == null)
        {
            GameObject imageObj = new GameObject("ProximityMessageImage");
            imageObj.transform.SetParent(messageCanvas.transform);

            messageImage = imageObj.AddComponent<Image>();

            // Create a default sprite if none provided
            if (defaultMessageSprite == null && customMessageSprite == null)
            {
                defaultMessageSprite = CreateDefaultMessageSprite();
            }

            // Set the sprite (prefer custom, fallback to default)
            messageImage.sprite = customMessageSprite != null ? customMessageSprite : defaultMessageSprite;
            messageImage.color = Color.white;
            messageImage.preserveAspect = true;

            // Position the image
            RectTransform rectTransform = messageImage.GetComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
            rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
            rectTransform.pivot = new Vector2(0.5f, 0.5f);
            rectTransform.anchoredPosition = messagePosition;
            rectTransform.sizeDelta = messageSize;
        }
        
        // Start with message hidden
        canvasGroup.alpha = 0f;
        targetAlpha = 0f;
        
        Debug.Log("Proximity message UI created successfully");
    }

    private Sprite CreateDefaultMessageSprite()
    {
        // Create a simple colored rectangle as default sprite
        Texture2D texture = new Texture2D(300, 100);
        Color[] pixels = new Color[300 * 100];

        // Create a simple gradient background
        for (int y = 0; y < 100; y++)
        {
            for (int x = 0; x < 300; x++)
            {
                float alpha = 0.8f;
                if (x < 10 || x > 290 || y < 10 || y > 90)
                {
                    // Border
                    pixels[y * 300 + x] = new Color(1f, 1f, 1f, alpha);
                }
                else
                {
                    // Interior with gradient
                    float gradient = Mathf.Lerp(0.2f, 0.6f, (float)y / 100f);
                    pixels[y * 300 + x] = new Color(0.1f, 0.5f, 1f, alpha * gradient);
                }
            }
        }

        texture.SetPixels(pixels);
        texture.Apply();

        return Sprite.Create(texture, new Rect(0, 0, 300, 100), new Vector2(0.5f, 0.5f));
    }
    
    private void Initialize()
    {
        // Find proximity selector
        proximitySelector = FindObjectOfType<JrsVehicleProximitySelector>();
        if (proximitySelector == null)
        {
            Debug.LogError("JrsVehicleProximityMessage: JrsVehicleProximitySelector not found!");
            return;
        }
        
        // Subscribe to proximity selector events
        proximitySelector.OnNearestVehicleChanged += OnNearestVehicleChanged;
        
        Debug.Log("JrsVehicleProximityMessage initialized successfully");
    }
    
    private void OnNearestVehicleChanged(JrsVehicleController nearestVehicle)
    {
        this.nearestVehicle = nearestVehicle;
        
        if (nearestVehicle != null)
        {
            ShowMessage(nearestVehicle);
        }
        else
        {
            HideMessage();
        }
    }
    
    private void ShowMessage(JrsVehicleController vehicle)
    {
        if (vehicle == null) return;
        
        // Show the image message (no text needed)
        targetAlpha = 1f;
        isMessageVisible = true;
        messageTimer = messageDuration;
        
        Debug.Log($"Showing proximity image message for vehicle: {vehicle.name}");
    }
    
    private void HideMessage()
    {
        targetAlpha = 0f;
        isMessageVisible = false;
        messageTimer = 0f;
        
        Debug.Log("Hiding proximity image message");
    }
    
    private void UpdateMessageVisibility()
    {
        if (isMessageVisible && messageTimer > 0f)
        {
            messageTimer -= Time.deltaTime;
            
            // Keep message visible while near vehicle
            if (nearestVehicle != null && proximitySelector != null && proximitySelector.CanSelectNearestVehicle())
            {
                messageTimer = messageDuration; // Reset timer to keep message visible
            }
        }
        
        // Hide message if timer expired and no nearby vehicle
        if (messageTimer <= 0f && (nearestVehicle == null || !proximitySelector.CanSelectNearestVehicle()))
        {
            HideMessage();
        }
    }
    
    private void UpdateMessageFade()
    {
        if (canvasGroup != null)
        {
            // Smoothly fade in/out
            canvasGroup.alpha = Mathf.MoveTowards(canvasGroup.alpha, targetAlpha, fadeSpeed * Time.deltaTime);
        }
    }
    
    // Public methods for configuration
    // Public methods for configuration
    public void SetMessagePosition(Vector2 position)
    {
        messagePosition = position;
        if (messageImage != null)
        {
            messageImage.GetComponent<RectTransform>().anchoredPosition = position;
        }
    }

    public void SetMessageSize(Vector2 size)
    {
        messageSize = size;
        if (messageImage != null)
        {
            messageImage.GetComponent<RectTransform>().sizeDelta = size;
        }
    }

    public void SetCustomSprite(Sprite sprite)
    {
        customMessageSprite = sprite;
        if (messageImage != null)
        {
            messageImage.sprite = sprite != null ? sprite : defaultMessageSprite;
        }
    }

    public void SetImageColor(Color color)
    {
        if (messageImage != null)
        {
            messageImage.color = color;
        }
    }
    
    void OnDestroy()
    {
        // Unsubscribe from events
        if (proximitySelector != null)
        {
            proximitySelector.OnNearestVehicleChanged -= OnNearestVehicleChanged;
        }
    }
}
