using UnityEngine;

/// <summary>
/// Simple test script to verify the Vehicle Proximity Selection System is working
/// Add this to any GameObject in your scene to test the system
/// </summary>
public class VehicleProximitySystemTest : MonoBehaviour
{
    [Header("Test Settings")]
    public KeyCode testKey = KeyCode.T;
    
    private JrsVehicleSelector vehicleSelector;
    private JrsVehicleProximitySelector proximitySelector;
    private JrsVehicleSelectionBillboard selectionBillboard;
    
    void Start()
    {
        // Find all system components
        vehicleSelector = FindObjectOfType<JrsVehicleSelector>();
        proximitySelector = FindObjectOfType<JrsVehicleProximitySelector>();
        selectionBillboard = FindObjectOfType<JrsVehicleSelectionBillboard>();
        
        // Run initial test
        Invoke(nameof(RunSystemTest), 2f); // Wait 2 seconds for initialization
    }
    
    void Update()
    {
        if (Input.GetKeyDown(testKey))
        {
            RunSystemTest();
        }
    }
    
    public void RunSystemTest()
    {
        Debug.Log("=== VEHICLE PROXIMITY SYSTEM TEST ===");
        
        // Test 1: Check if all components exist
        bool allComponentsFound = TestComponentExistence();
        
        // Test 2: Check system initialization
        bool systemInitialized = TestSystemInitialization();
        
        // Test 3: Check proximity detection functionality
        bool proximityWorking = TestProximityDetection();
        
        // Test 4: Check billboard functionality
        bool billboardWorking = TestBillboardFunctionality();
        
        // Summary
        Debug.Log("=== TEST SUMMARY ===");
        Debug.Log($"Components Found: {(allComponentsFound ? "PASS" : "FAIL")}");
        Debug.Log($"System Initialized: {(systemInitialized ? "PASS" : "FAIL")}");
        Debug.Log($"Proximity Detection: {(proximityWorking ? "PASS" : "FAIL")}");
        Debug.Log($"Billboard Functionality: {(billboardWorking ? "PASS" : "FAIL")}");
        
        bool overallPass = allComponentsFound && systemInitialized && proximityWorking && billboardWorking;
        Debug.Log($"OVERALL TEST RESULT: {(overallPass ? "PASS ✓" : "FAIL ✗")}");
        
        if (!overallPass)
        {
            Debug.LogWarning("Some tests failed. Check the individual test results above for details.");
        }
        else
        {
            Debug.Log("All tests passed! The Vehicle Proximity Selection System should be working correctly.");
        }
    }
    
    private bool TestComponentExistence()
    {
        Debug.Log("--- Testing Component Existence ---");
        
        bool vehicleSelectorExists = vehicleSelector != null;
        bool proximitySelectorExists = proximitySelector != null;
        bool billboardExists = selectionBillboard != null;
        
        Debug.Log($"JrsVehicleSelector: {(vehicleSelectorExists ? "Found" : "Missing")}");
        Debug.Log($"JrsVehicleProximitySelector: {(proximitySelectorExists ? "Found" : "Missing")}");
        Debug.Log($"JrsVehicleSelectionBillboard: {(billboardExists ? "Found" : "Missing")}");
        
        return vehicleSelectorExists && proximitySelectorExists && billboardExists;
    }
    
    private bool TestSystemInitialization()
    {
        Debug.Log("--- Testing System Initialization ---");
        
        if (vehicleSelector == null)
        {
            Debug.LogError("Cannot test initialization - VehicleSelector not found");
            return false;
        }
        
        bool proximityEnabled = vehicleSelector.enableProximitySelection;
        bool hasVehicles = vehicleSelector.vehicles != null && vehicleSelector.vehicles.Count > 0;
        bool currentVehicleSet = vehicleSelector.GetCurrentVehicle() != null;
        
        Debug.Log($"Proximity Selection Enabled: {proximityEnabled}");
        Debug.Log($"Has Vehicles: {hasVehicles} (Count: {vehicleSelector.vehicles?.Count ?? 0})");
        Debug.Log($"Current Vehicle Set: {currentVehicleSet}");
        
        return proximityEnabled && hasVehicles && currentVehicleSet;
    }
    
    private bool TestProximityDetection()
    {
        Debug.Log("--- Testing Proximity Detection ---");
        
        if (proximitySelector == null)
        {
            Debug.LogError("Cannot test proximity detection - ProximitySelector not found");
            return false;
        }
        
        // Test basic functionality
        float detectionRange = proximitySelector.DetectionRange;
        int nearbyCount = proximitySelector.GetNearbyVehicleCount();
        bool canSelect = proximitySelector.CanSelectNearestVehicle();
        var nearestVehicle = proximitySelector.GetNearestSelectableVehicle();
        
        Debug.Log($"Detection Range: {detectionRange}m");
        Debug.Log($"Nearby Vehicles: {nearbyCount}");
        Debug.Log($"Can Select Nearest: {canSelect}");
        Debug.Log($"Nearest Vehicle: {(nearestVehicle?.name ?? "None")}");
        
        // Basic functionality test passes if we can access all properties without errors
        return detectionRange > 0;
    }
    
    private bool TestBillboardFunctionality()
    {
        Debug.Log("--- Testing Billboard Functionality ---");
        
        if (selectionBillboard == null)
        {
            Debug.LogError("Cannot test billboard - SelectionBillboard not found");
            return false;
        }
        
        bool gameObjectActive = selectionBillboard.gameObject.activeInHierarchy;
        bool isVisible = selectionBillboard.IsVisible;
        var targetVehicle = selectionBillboard.TargetVehicle;
        
        Debug.Log($"Billboard GameObject Active: {gameObjectActive}");
        Debug.Log($"Billboard Visible: {isVisible}");
        Debug.Log($"Target Vehicle: {(targetVehicle?.name ?? "None")}");
        
        // Test canvas components
        var canvas = selectionBillboard.GetComponent<Canvas>();
        var canvasGroup = selectionBillboard.GetComponent<CanvasGroup>();
        
        bool hasCanvas = canvas != null;
        bool hasCanvasGroup = canvasGroup != null;
        
        Debug.Log($"Has Canvas: {hasCanvas}");
        Debug.Log($"Has CanvasGroup: {hasCanvasGroup}");
        
        if (hasCanvasGroup)
        {
            Debug.Log($"Canvas Alpha: {canvasGroup.alpha}");
        }
        
        return gameObjectActive && hasCanvas;
    }
}
