using UnityEngine;
using System.Collections;

public class JrsCameraManager : MonoBehaviour
{
    public Camera[] cameras;
    // toggleKeys removed - camera switching now handled by New Input System

    private int activeCameraIndex = 0;

    private void Start()
    {
        // Disable all cameras except the first one
        for (int i = 1; i < cameras.Length; i++)
        {
            cameras[i].gameObject.SetActive(false);
        }

        // Ensure proper AudioListener setup
        SetupAudioListeners();

        // Force a camera reset after a short delay to ensure everything is synchronized
        StartCoroutine(ForceInitialCameraReset());
    }

    private void Update()
    {
        // Individual camera toggle keys are now deprecated
        // Camera switching is handled by the input controller via SwitchToNextCamera()
        // This Update method is kept empty for backward compatibility
    }

    public void SwitchToNextCamera()
    {
        int nextIndex = (activeCameraIndex + 1) % cameras.Length;
        SwitchToCamera(nextIndex);
    }

    private void SwitchToCamera(int index)
    {
        // Disable the currently active camera
        cameras[activeCameraIndex].gameObject.SetActive(false);

        // Enable the new camera
        cameras[index].gameObject.SetActive(true);

        // Update the active camera index
        activeCameraIndex = index;

        // Ensure proper AudioListener management
        ManageAudioListeners();
    }

    public void SetActiveCamera(Transform vehicleTransform)
    {
        if (vehicleTransform == null)
        {
            Debug.LogError("Vehicle transform is null");
            return;
        }

        foreach (Camera cam in cameras)
        {
            if (cam == null) continue;

            JrsDashCamera dashCam = cam.GetComponent<JrsDashCamera>();
            if (dashCam != null)
            {
                Transform player = vehicleTransform.Find("player");
                if (player != null) dashCam.player = player;
            }

            JrsOrbitCamera orbitCam = cam.GetComponent<JrsOrbitCamera>();
            if (orbitCam != null)
            {
                Transform target = vehicleTransform.Find("target");
                if (target != null) orbitCam.target = target;
            }

            JrsFollowCamera followCam = cam.GetComponent<JrsFollowCamera>();
            if (followCam != null)
            {
                Transform target = vehicleTransform.Find("target");
                if (target != null) followCam.target = target;
            }
        }
    }

    public void RefreshCameraTargets(Transform vehicleTransform)
    {
        SetActiveCamera(vehicleTransform);
    }

    /// <summary>
    /// Aggressively cleans up and sets up AudioListeners on all cameras
    /// </summary>
    private void SetupAudioListeners()
    {
        Debug.Log("🔧 Starting aggressive AudioListener cleanup and setup...");

        // STEP 1: Find and disable ALL AudioListeners in the scene
        AudioListener[] allListeners = FindObjectsOfType<AudioListener>();
        Debug.Log($"Found {allListeners.Length} AudioListeners in scene - disabling all...");

        foreach (AudioListener listener in allListeners)
        {
            listener.enabled = false;
            Debug.Log($"  Disabled AudioListener on: {listener.gameObject.name}");
        }

        // STEP 2: Remove any existing AudioListeners from cameras and add fresh ones
        for (int i = 0; i < cameras.Length; i++)
        {
            if (cameras[i] == null) continue;

            // Remove existing AudioListener if present
            AudioListener existingListener = cameras[i].GetComponent<AudioListener>();
            if (existingListener != null)
            {
                DestroyImmediate(existingListener);
                Debug.Log($"  Removed existing AudioListener from: {cameras[i].name}");
            }

            // Add fresh AudioListener
            AudioListener newListener = cameras[i].gameObject.AddComponent<AudioListener>();
            newListener.enabled = (i == activeCameraIndex);

            Debug.Log($"  Added fresh AudioListener to: {cameras[i].name} (Enabled: {newListener.enabled})");
        }

        Debug.Log($"✅ AudioListener aggressive setup complete. Active camera: {activeCameraIndex} ({cameras[activeCameraIndex].name})");
    }

    /// <summary>
    /// Public method to refresh AudioListener setup (called by vehicle selector)
    /// </summary>
    public void RefreshAudioListeners()
    {
        SetupAudioListeners();
    }

    /// <summary>
    /// Public method to force audio reset - can be called if audio issues persist
    /// </summary>
    [ContextMenu("Force Audio Reset")]
    public void ForceAudioReset()
    {
        Debug.Log("🔧 Manual audio reset triggered...");
        SetupAudioListeners();
        ManageAudioListeners();
        ValidateAudioSetup();
    }

    /// <summary>
    /// Aggressively manages AudioListeners when switching cameras
    /// </summary>
    private void ManageAudioListeners()
    {
        Debug.Log($"🔄 Switching AudioListener to camera {activeCameraIndex}: {cameras[activeCameraIndex].name}");

        // STEP 1: Disable ALL AudioListeners in the scene (aggressive approach)
        AudioListener[] allListeners = FindObjectsOfType<AudioListener>();
        foreach (AudioListener listener in allListeners)
        {
            listener.enabled = false;
        }

        // STEP 2: Enable only the AudioListener on the active camera
        AudioListener activeListener = cameras[activeCameraIndex].GetComponent<AudioListener>();
        if (activeListener != null)
        {
            activeListener.enabled = true;
            Debug.Log($"✅ AudioListener enabled on: {cameras[activeCameraIndex].name}");
            Debug.Log($"   Camera position: {cameras[activeCameraIndex].transform.position}");
        }
        else
        {
            Debug.LogError($"❌ No AudioListener found on active camera: {cameras[activeCameraIndex].name}");
            // Force add one if missing
            activeListener = cameras[activeCameraIndex].gameObject.AddComponent<AudioListener>();
            activeListener.enabled = true;
            Debug.Log($"🔧 Force-added AudioListener to: {cameras[activeCameraIndex].name}");
        }
    }

    /// <summary>
    /// Forces an initial camera reset to ensure proper synchronization
    /// This implements your suggested approach of resetting the camera system on game start
    /// </summary>
    private System.Collections.IEnumerator ForceInitialCameraReset()
    {
        // Wait for all systems to initialize
        yield return new WaitForSeconds(0.5f);

        Debug.Log("🔄 Forcing initial camera reset for audio synchronization...");

        // Force a camera switch cycle to reset everything
        int originalIndex = activeCameraIndex;

        // Switch to next camera and back to ensure proper initialization
        if (cameras.Length > 1)
        {
            SwitchToNextCamera(); // Switch away
            yield return new WaitForSeconds(0.1f);

            // Switch back to original camera (Follow camera)
            while (activeCameraIndex != originalIndex)
            {
                SwitchToNextCamera();
                yield return new WaitForSeconds(0.1f);
            }
        }
        else
        {
            // If only one camera, just refresh its AudioListener
            ManageAudioListeners();
        }

        Debug.Log($"✅ Initial camera reset complete. Active camera: {cameras[activeCameraIndex].name}");

        // Final validation
        yield return new WaitForSeconds(0.2f);
        ValidateAudioSetup();
    }

    /// <summary>
    /// Validates the current audio setup and logs detailed information
    /// </summary>
    private void ValidateAudioSetup()
    {
        Debug.Log("🔍 Validating final audio setup...");

        AudioListener activeListener = cameras[activeCameraIndex].GetComponent<AudioListener>();
        if (activeListener != null && activeListener.enabled)
        {
            Debug.Log($"✅ AudioListener properly active on: {cameras[activeCameraIndex].name}");
            Debug.Log($"   Position: {cameras[activeCameraIndex].transform.position}");

            // Check if it's a Follow camera and log target info
            JrsFollowCamera followCam = cameras[activeCameraIndex].GetComponent<JrsFollowCamera>();
            if (followCam != null)
            {
                Debug.Log($"   Follow Camera Target: {(followCam.target != null ? followCam.target.name : "NULL")}");
                if (followCam.target != null)
                {
                    Debug.Log($"   Target Position: {followCam.target.position}");
                }
            }
        }
        else
        {
            Debug.LogError($"❌ AudioListener validation failed on: {cameras[activeCameraIndex].name}");
        }

        // Count total active listeners
        AudioListener[] allListeners = FindObjectsOfType<AudioListener>();
        int activeCount = 0;
        foreach (AudioListener listener in allListeners)
        {
            if (listener.enabled) activeCount++;
        }

        Debug.Log($"   Total active AudioListeners in scene: {activeCount}");

        if (activeCount != 1)
        {
            Debug.LogWarning($"⚠️ Expected 1 active AudioListener, found {activeCount}");
        }
    }
}
