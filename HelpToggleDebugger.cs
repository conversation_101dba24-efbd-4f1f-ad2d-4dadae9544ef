using UnityEngine;
using UnityEngine.InputSystem;

/// <summary>
/// Debug script to test F1 key input detection
/// Attach this to any GameObject to test if F1 key is being detected
/// </summary>
public class HelpToggleDebugger : MonoBehaviour
{
    [Header("Debug Settings")]
    public bool enableDebugLogs = true;
    public bool testLegacyInput = true;
    public bool testNewInputSystem = true;
    
    [Header("Status")]
    public bool f1KeyDetected = false;
    public bool newInputSystemAvailable = false;
    
    private VehicleInputActions testInputActions;
    private InputAction testHelpToggleAction;
    
    void Start()
    {
        if (testNewInputSystem)
        {
            try
            {
                testInputActions = new VehicleInputActions();
                testHelpToggleAction = testInputActions.Vehicle.HelpToggle;
                testHelpToggleAction.performed += OnTestHelpToggle;
                testInputActions.Enable();
                newInputSystemAvailable = true;
                
                if (enableDebugLogs)
                {
                    Debug.Log("HelpToggleDebugger: New Input System initialized successfully");
                    Debug.Log($"HelpToggleDebugger: Help toggle bindings count: {testHelpToggleAction.bindings.Count}");
                    for (int i = 0; i < testHelpToggleAction.bindings.Count; i++)
                    {
                        Debug.Log($"  Binding {i}: {testHelpToggleAction.bindings[i].path}");
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"HelpToggleDebugger: Failed to initialize New Input System: {e.Message}");
                newInputSystemAvailable = false;
            }
        }
    }
    
    void Update()
    {
        f1KeyDetected = false;
        
        // Test legacy input
        if (testLegacyInput && Input.GetKeyDown(KeyCode.F1))
        {
            f1KeyDetected = true;
            if (enableDebugLogs)
            {
                Debug.Log("HelpToggleDebugger: F1 detected via Legacy Input System!");
            }
        }
        
        // Test new input system via WasPressedThisFrame
        if (testNewInputSystem && newInputSystemAvailable && testHelpToggleAction != null)
        {
            if (testHelpToggleAction.WasPressedThisFrame())
            {
                f1KeyDetected = true;
                if (enableDebugLogs)
                {
                    Debug.Log("HelpToggleDebugger: F1 detected via New Input System (WasPressedThisFrame)!");
                }
            }
        }
    }
    
    private void OnTestHelpToggle(InputAction.CallbackContext context)
    {
        f1KeyDetected = true;
        if (enableDebugLogs)
        {
            Debug.Log($"HelpToggleDebugger: F1 detected via New Input System callback! Phase: {context.phase}, Control: {context.control}");
        }
    }
    
    void OnDestroy()
    {
        if (testInputActions != null)
        {
            if (testHelpToggleAction != null)
            {
                testHelpToggleAction.performed -= OnTestHelpToggle;
            }
            testInputActions.Dispose();
        }
    }
    
    void OnGUI()
    {
        if (enableDebugLogs)
        {
            GUILayout.BeginArea(new Rect(10, 10, 400, 200));
            GUILayout.Label("=== Help Toggle Debugger ===");
            GUILayout.Label($"Legacy Input Available: {testLegacyInput}");
            GUILayout.Label($"New Input System Available: {newInputSystemAvailable}");
            GUILayout.Label($"F1 Key Detected: {f1KeyDetected}");
            GUILayout.Label("Press F1 to test input detection");
            
            if (GUILayout.Button("Test Legacy F1"))
            {
                Debug.Log($"Manual test - Legacy Input.GetKey(F1): {Input.GetKey(KeyCode.F1)}");
            }
            
            if (newInputSystemAvailable && GUILayout.Button("Test New Input F1"))
            {
                Debug.Log($"Manual test - New Input WasPressedThisFrame: {testHelpToggleAction?.WasPressedThisFrame()}");
                Debug.Log($"Manual test - New Input IsPressed: {testHelpToggleAction?.IsPressed()}");
            }
            
            GUILayout.EndArea();
        }
    }
}
