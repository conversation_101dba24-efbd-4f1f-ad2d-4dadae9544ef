using UnityEngine;
using UnityEngine.UI;

public class VehicleProximityImageSetup : MonoBehaviour
{
    [Header("System Setup")]
    [SerializeField] private bool autoSetupOnStart = true;
    [SerializeField] private bool useImageMessage = true; // Use image instead of text
    
    [Header("Vehicle System References")]
    [SerializeField] private JrsVehicleSelector vehicleSelector;
    [SerializeField] private JrsVehicleProximitySelector proximitySelector;
    [SerializeField] private JrsVehicleProximityImageMessage imageMessage;
    
    [Header("Message Configuration")]
    [SerializeField] private Vector2 messagePosition = new Vector2(0, 100);
    [SerializeField] private Vector2 messageSize = new Vector2(300, 100);
    [SerializeField] private Sprite customMessageSprite;
    [SerializeField] private Color messageColor = Color.white;
    [SerializeField] private float detectionRange = 10f;
    
    void Start()
    {
        if (autoSetupOnStart)
        {
            SetupProximitySystem();
        }
    }
    
    [ContextMenu("Setup Proximity System")]
    public void SetupProximitySystem()
    {
        Debug.Log("Setting up Vehicle Proximity System...");
        
        // Find or create vehicle selector
        SetupVehicleSelector();
        
        // Find or create proximity selector
        SetupProximitySelector();
        
        // Setup image message system
        if (useImageMessage)
        {
            SetupImageMessage();
        }
        
        Debug.Log("Vehicle Proximity System setup complete!");
    }
    
    private void SetupVehicleSelector()
    {
        if (vehicleSelector == null)
        {
            vehicleSelector = FindObjectOfType<JrsVehicleSelector>();
            
            if (vehicleSelector == null)
            {
                Debug.LogError("No JrsVehicleSelector found! Please add one to your scene.");
                return;
            }
        }
        
        Debug.Log($"Vehicle Selector found: {vehicleSelector.name}");
    }
    
    private void SetupProximitySelector()
    {
        if (proximitySelector == null)
        {
            proximitySelector = FindObjectOfType<JrsVehicleProximitySelector>();
            
            if (proximitySelector == null)
            {
                // Create proximity selector on the same object as vehicle selector
                if (vehicleSelector != null)
                {
                    proximitySelector = vehicleSelector.gameObject.AddComponent<JrsVehicleProximitySelector>();
                    Debug.Log("Created JrsVehicleProximitySelector component");
                }
                else
                {
                    Debug.LogError("Cannot create proximity selector without vehicle selector!");
                    return;
                }
            }
        }
        
        // Configure proximity selector
        proximitySelector.SetVehicleSelector(vehicleSelector);
        proximitySelector.SetDetectionRange(detectionRange);
        
        Debug.Log($"Proximity Selector configured: {proximitySelector.name}");
    }
    
    private void SetupImageMessage()
    {
        if (imageMessage == null)
        {
            imageMessage = FindObjectOfType<JrsVehicleProximityImageMessage>();
            
            if (imageMessage == null)
            {
                // Create image message system
                GameObject messageObj = new GameObject("VehicleProximityImageMessage");
                imageMessage = messageObj.AddComponent<JrsVehicleProximityImageMessage>();
                Debug.Log("Created JrsVehicleProximityImageMessage component");
            }
        }
        
        // Configure image message
        imageMessage.SetMessagePosition(messagePosition);
        imageMessage.SetMessageSize(messageSize);
        imageMessage.SetImageColor(messageColor);
        
        if (customMessageSprite != null)
        {
            imageMessage.SetCustomSprite(customMessageSprite);
        }
        
        Debug.Log($"Image Message configured: {imageMessage.name}");
    }
    
    [ContextMenu("Test Proximity Detection")]
    public void TestProximityDetection()
    {
        if (proximitySelector == null)
        {
            Debug.LogWarning("No proximity selector found!");
            return;
        }
        
        var nearestVehicle = proximitySelector.GetNearestSelectableVehicle();
        if (nearestVehicle != null)
        {
            Debug.Log($"Nearest vehicle: {nearestVehicle.name} at distance: {proximitySelector.GetDistanceToNearestVehicle():F1}m");
        }
        else
        {
            Debug.Log("No nearby vehicles detected");
        }
        
        Debug.Log($"Nearby vehicle count: {proximitySelector.GetNearbyVehicleCount()}");
    }
    
    [ContextMenu("Force Show Message")]
    public void ForceShowMessage()
    {
        if (imageMessage == null)
        {
            Debug.LogWarning("No image message component found!");
            return;
        }
        
        // Temporarily show message for testing
        var nearestVehicle = proximitySelector?.GetNearestSelectableVehicle();
        if (nearestVehicle != null)
        {
            Debug.Log("Forcing message display for testing");
            // The message will show automatically when a vehicle is detected
        }
        else
        {
            Debug.Log("No vehicle to show message for. Move closer to a vehicle.");
        }
    }
    
    // Public methods for runtime configuration
    public void SetMessageSprite(Sprite sprite)
    {
        customMessageSprite = sprite;
        if (imageMessage != null)
        {
            imageMessage.SetCustomSprite(sprite);
        }
    }
    
    public void SetMessageColor(Color color)
    {
        messageColor = color;
        if (imageMessage != null)
        {
            imageMessage.SetImageColor(color);
        }
    }
    
    public void SetMessagePosition(Vector2 position)
    {
        messagePosition = position;
        if (imageMessage != null)
        {
            imageMessage.SetMessagePosition(position);
        }
    }
    
    public void SetMessageSize(Vector2 size)
    {
        messageSize = size;
        if (imageMessage != null)
        {
            imageMessage.SetMessageSize(size);
        }
    }
    
    public void SetDetectionRange(float range)
    {
        detectionRange = range;
        if (proximitySelector != null)
        {
            proximitySelector.SetDetectionRange(range);
        }
    }
    
    // Validation
    void OnValidate()
    {
        // Ensure reasonable values
        messageSize.x = Mathf.Max(50f, messageSize.x);
        messageSize.y = Mathf.Max(30f, messageSize.y);
        detectionRange = Mathf.Max(1f, detectionRange);
    }
}
