using UnityEngine;

/// <summary>
/// Helper script to configure the input system for the vehicle controller
/// This script helps ensure that the help toggle (F1) works correctly
/// </summary>
public class InputSystemSetupHelper : MonoBehaviour
{
    [Header("Input System Configuration")]
    [SerializeField] private bool useNewInputSystem = true;
    [SerializeField] private bool autoConfigureOnStart = true;
    
    [Header("Status")]
    [SerializeField] private bool vehicleControllerFound = false;
    [SerializeField] private bool newInputControllerFound = false;
    [SerializeField] private bool oldInputControllerFound = false;
    [SerializeField] private bool helpToggleObjectFound = false;
    
    private JrsVehicleController vehicleController;
    private JrsNewInputController newInputController;
    private JrsInputController oldInputController;
    private JrsToggleObject helpToggleObject;
    
    void Start()
    {
        if (autoConfigureOnStart)
        {
            ConfigureInputSystem();
        }
    }
    
    [ContextMenu("Configure Input System")]
    public void ConfigureInputSystem()
    {
        Debug.Log("=== Input System Setup Helper ===");
        
        // Find all relevant components
        FindComponents();
        
        // Configure the input system
        if (vehicleController != null)
        {
            vehicleController.useNewInputSystem = useNewInputSystem;
            Debug.Log($"✓ Set vehicle controller to use: {(useNewInputSystem ? "New Input System" : "Legacy Input System")}");
        }
        
        // Enable/disable appropriate input controllers
        if (useNewInputSystem)
        {
            if (newInputController != null)
            {
                newInputController.enabled = true;
                Debug.Log("✓ Enabled JrsNewInputController");
            }
            else
            {
                Debug.LogError("✗ JrsNewInputController not found! New Input System cannot work without it.");
            }
            
            if (oldInputController != null)
            {
                oldInputController.enabled = false;
                Debug.Log("✓ Disabled JrsInputController (old) to prevent conflicts");
            }
        }
        else
        {
            if (oldInputController != null)
            {
                oldInputController.enabled = true;
                Debug.Log("✓ Enabled JrsInputController (legacy)");
            }
            
            if (newInputController != null)
            {
                newInputController.enabled = false;
                Debug.Log("✓ Disabled JrsNewInputController to prevent conflicts");
            }
        }
        
        // Configure help toggle object
        if (helpToggleObject != null)
        {
            helpToggleObject.useNewInputSystem = useNewInputSystem;
            Debug.Log($"✓ Set help toggle object to use: {(useNewInputSystem ? "New Input System" : "Legacy Input System")}");
        }
        
        // Final status report
        GenerateStatusReport();
    }
    
    private void FindComponents()
    {
        vehicleController = FindObjectOfType<JrsVehicleController>();
        newInputController = FindObjectOfType<JrsNewInputController>();
        oldInputController = FindObjectOfType<JrsInputController>();
        helpToggleObject = FindObjectOfType<JrsToggleObject>();
        
        vehicleControllerFound = vehicleController != null;
        newInputControllerFound = newInputController != null;
        oldInputControllerFound = oldInputController != null;
        helpToggleObjectFound = helpToggleObject != null;
    }
    
    private void GenerateStatusReport()
    {
        Debug.Log("=== Status Report ===");
        Debug.Log($"Vehicle Controller Found: {vehicleControllerFound}");
        Debug.Log($"New Input Controller Found: {newInputControllerFound}");
        Debug.Log($"Old Input Controller Found: {oldInputControllerFound}");
        Debug.Log($"Help Toggle Object Found: {helpToggleObjectFound}");
        
        if (useNewInputSystem)
        {
            if (newInputControllerFound && vehicleControllerFound)
            {
                Debug.Log("✅ New Input System is properly configured!");
                Debug.Log("📋 F1 key should work for help toggle");
            }
            else
            {
                Debug.LogError("❌ New Input System configuration incomplete!");
                if (!newInputControllerFound)
                    Debug.LogError("   - Missing JrsNewInputController component");
                if (!vehicleControllerFound)
                    Debug.LogError("   - Missing JrsVehicleController component");
            }
        }
        else
        {
            if (oldInputControllerFound || vehicleControllerFound)
            {
                Debug.Log("✅ Legacy Input System is configured!");
                Debug.Log("📋 F1 key should work for help toggle (legacy mode)");
            }
            else
            {
                Debug.LogError("❌ Legacy Input System configuration incomplete!");
            }
        }
        
        if (!helpToggleObjectFound)
        {
            Debug.LogWarning("⚠️ No JrsToggleObject found - help toggle will not work");
        }
    }
    
    [ContextMenu("Test F1 Key Detection")]
    public void TestF1KeyDetection()
    {
        Debug.Log("=== Testing F1 Key Detection ===");
        Debug.Log("Press F1 now to test...");
        
        // Add the HelpToggleDebugger component temporarily for testing
        GameObject testObject = new GameObject("F1_Test_Debugger");
        testObject.AddComponent<HelpToggleDebugger>();
        
        Debug.Log("✓ Added temporary F1 test debugger. Check console for F1 key presses.");
        Debug.Log("The test debugger will be automatically destroyed after 30 seconds.");
        
        // Auto-destroy after 30 seconds
        Destroy(testObject, 30f);
    }
    
    [ContextMenu("Switch to New Input System")]
    public void SwitchToNewInputSystem()
    {
        useNewInputSystem = true;
        ConfigureInputSystem();
    }
    
    [ContextMenu("Switch to Legacy Input System")]
    public void SwitchToLegacyInputSystem()
    {
        useNewInputSystem = false;
        ConfigureInputSystem();
    }
}
